// api.config.js 的类型声明文件
interface RequestConfig {
	timeout: number;
	headers: {
		[key: string]: string;
	};
}

interface GenerateConfig {
	serversPath: string;
	requestLibPath: string;
	projectName: string;
	namespace: string;
	apiPrefix: string;
	mock: boolean;
}

interface AuthConfig {
	type: "bearer" | "basic" | "apikey";
	tokenKey: string;
	headerName: string;
}

interface ApiConfig {
	swaggerUrl: string;
	baseUrl: string;
	generate: GenerateConfig;
	requestConfig: RequestConfig;
	auth: AuthConfig;
}

declare const config: ApiConfig;
export = config;
