var R = Object.defineProperty;
var q = (e, r, t) => r in e ? R(e, r, { enumerable: !0, configurable: !0, writable: !0, value: t }) : e[r] = t;
var u = (e, r, t) => q(e, typeof r != "symbol" ? r + "" : r, t);
const I = "%[a-f0-9]{2}", F = new RegExp("(" + I + ")|([^%]+?)", "gi"), b = new RegExp("(" + I + ")+", "gi");
console.log('加载js文件');
function l(e, r) {
	try {
		return [decodeURIComponent(e.join(""))];
	} catch {
	}
	if (e.length === 1)
		return e;
	r = r || 1;
	const t = e.slice(0, r), n = e.slice(r);
	return Array.prototype.concat.call([], l(t), l(n));
}
function D(e) {
	try {
		return decodeURIComponent(e);
	} catch {
		let r = e.match(F) || [];
		for (let t = 1; t < r.length; t++)
			e = l(r, t).join(""), r = e.match(F) || [];
		return e;
	}
}
function L(e) {
	const r = {
		"%FE%FF": "��",
		"%FF%FE": "��"
	};
	let t = b.exec(e);
	for (; t;) {
		try {
			r[t[0]] = decodeURIComponent(t[0]);
		} catch {
			const a = D(t[0]);
			a !== t[0] && (r[t[0]] = a);
		}
		t = b.exec(e);
	}
	r["%C2"] = "�";
	const n = Object.keys(r);
	for (const a of n)
		e = e.replace(new RegExp(a, "g"), r[a]);
	return e;
}
function P(e) {
	if (typeof e != "string")
		throw new TypeError("Expected `encodedURI` to be of type `string`, got `" + typeof e + "`");
	try {
		return decodeURIComponent(e);
	} catch {
		return L(e);
	}
}
function M(e, r) {
	if (!(typeof e == "string" && typeof r == "string"))
		throw new TypeError("Expected the arguments to be of type `string`");
	if (e === "" || r === "")
		return [];
	const t = e.indexOf(r);
	return t === -1 ? [] : [
		e.slice(0, t),
		e.slice(t + r.length)
	];
}
function k(e, r) {
	const t = {};
	if (Array.isArray(r))
		for (const n of r) {
			const a = Object.getOwnPropertyDescriptor(e, n);
			a != null && a.enumerable && Object.defineProperty(t, n, a);
		}
	else
		for (const n of Reflect.ownKeys(e)) {
			const a = Object.getOwnPropertyDescriptor(e, n);
			if (a.enumerable) {
				const i = e[n];
				r(n, i, e) && Object.defineProperty(t, n, a);
			}
		}
	return t;
}
const H = (e) => e == null, T = (e) => encodeURIComponent(e).replaceAll(/[!'()*]/g, (r) => `%${r.charCodeAt(0).toString(16).toUpperCase()}`), m = Symbol("encodeFragmentIdentifier");
function B(e) {
	switch (e.arrayFormat) {
		case "index":
			return (r) => (t, n) => {
				const a = t.length;
				return n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
					...t,
					[f(r, e), "[", a, "]"].join("")
				] : [
					...t,
					[f(r, e), "[", f(a, e), "]=", f(n, e)].join("")
				];
			};
		case "bracket":
			return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
				...t,
				[f(r, e), "[]"].join("")
			] : [
				...t,
				[f(r, e), "[]=", f(n, e)].join("")
			];
		case "colon-list-separator":
			return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
				...t,
				[f(r, e), ":list="].join("")
			] : [
				...t,
				[f(r, e), ":list=", f(n, e)].join("")
			];
		case "comma":
		case "separator":
		case "bracket-separator": {
			const r = e.arrayFormat === "bracket-separator" ? "[]=" : "=";
			return (t) => (n, a) => a === void 0 || e.skipNull && a === null || e.skipEmptyString && a === "" ? n : (a = a === null ? "" : a, n.length === 0 ? [[f(t, e), r, f(a, e)].join("")] : [[n, f(a, e)].join(e.arrayFormatSeparator)]);
		}
		default:
			return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
				...t,
				f(r, e)
			] : [
				...t,
				[f(r, e), "=", f(n, e)].join("")
			];
	}
}
function W(e) {
	let r;
	switch (e.arrayFormat) {
		case "index":
			return (t, n, a) => {
				if (r = /\[(\d*)]$/.exec(t), t = t.replace(/\[\d*]$/, ""), !r) {
					a[t] = n;
					return;
				}
				a[t] === void 0 && (a[t] = {}), a[t][r[1]] = n;
			};
		case "bracket":
			return (t, n, a) => {
				if (r = /(\[])$/.exec(t), t = t.replace(/\[]$/, ""), !r) {
					a[t] = n;
					return;
				}
				if (a[t] === void 0) {
					a[t] = [n];
					return;
				}
				a[t] = [...a[t], n];
			};
		case "colon-list-separator":
			return (t, n, a) => {
				if (r = /(:list)$/.exec(t), t = t.replace(/:list$/, ""), !r) {
					a[t] = n;
					return;
				}
				if (a[t] === void 0) {
					a[t] = [n];
					return;
				}
				a[t] = [...a[t], n];
			};
		case "comma":
		case "separator":
			return (t, n, a) => {
				const i = typeof n == "string" && n.includes(e.arrayFormatSeparator), s = typeof n == "string" && !i && d(n, e).includes(e.arrayFormatSeparator);
				n = s ? d(n, e) : n;
				const o = i || s ? n.split(e.arrayFormatSeparator).map((c) => d(c, e)) : n === null ? n : d(n, e);
				a[t] = o;
			};
		case "bracket-separator":
			return (t, n, a) => {
				const i = /(\[])$/.test(t);
				if (t = t.replace(/\[]$/, ""), !i) {
					a[t] = n && d(n, e);
					return;
				}
				const s = n === null ? [] : n.split(e.arrayFormatSeparator).map((o) => d(o, e));
				if (a[t] === void 0) {
					a[t] = s;
					return;
				}
				a[t] = [...a[t], ...s];
			};
		default:
			return (t, n, a) => {
				if (a[t] === void 0) {
					a[t] = n;
					return;
				}
				a[t] = [...[a[t]].flat(), n];
			};
	}
}
function E(e) {
	if (typeof e != "string" || e.length !== 1)
		throw new TypeError("arrayFormatSeparator must be single character string");
}
function f(e, r) {
	return r.encode ? r.strict ? T(e) : encodeURIComponent(e) : e;
}
function d(e, r) {
	return r.decode ? P(e) : e;
}
function x(e) {
	return Array.isArray(e) ? e.sort() : typeof e == "object" ? x(Object.keys(e)).sort((r, t) => Number(r) - Number(t)).map((r) => e[r]) : e;
}
function A(e) {
	const r = e.indexOf("#");
	return r !== -1 && (e = e.slice(0, r)), e;
}
function V(e) {
	let r = "";
	const t = e.indexOf("#");
	return t !== -1 && (r = e.slice(t)), r;
}
function w(e, r) {
	return r.parseNumbers && !Number.isNaN(Number(e)) && typeof e == "string" && e.trim() !== "" ? e = Number(e) : r.parseBooleans && e !== null && (e.toLowerCase() === "true" || e.toLowerCase() === "false") && (e = e.toLowerCase() === "true"), e;
}
function h(e) {
	e = A(e);
	const r = e.indexOf("?");
	return r === -1 ? "" : e.slice(r + 1);
}
function g(e, r) {
	r = {
		decode: !0,
		sort: !0,
		arrayFormat: "none",
		arrayFormatSeparator: ",",
		parseNumbers: !1,
		parseBooleans: !1,
		...r
	}, E(r.arrayFormatSeparator);
	const t = W(r), n = /* @__PURE__ */ Object.create(null);
	if (typeof e != "string" || (e = e.trim().replace(/^[?#&]/, ""), !e))
		return n;
	for (const a of e.split("&")) {
		if (a === "")
			continue;
		const i = r.decode ? a.replaceAll("+", " ") : a;
		let [s, o] = M(i, "=");
		s === void 0 && (s = i), o = o === void 0 ? null : ["comma", "separator", "bracket-separator"].includes(r.arrayFormat) ? o : d(o, r), t(d(s, r), o, n);
	}
	for (const [a, i] of Object.entries(n))
		if (typeof i == "object" && i !== null)
			for (const [s, o] of Object.entries(i))
				i[s] = w(o, r);
		else
			n[a] = w(i, r);
	return r.sort === !1 ? n : (r.sort === !0 ? Object.keys(n).sort() : Object.keys(n).sort(r.sort)).reduce((a, i) => {
		const s = n[i];
		return a[i] = s && typeof s == "object" && !Array.isArray(s) ? x(s) : s, a;
	}, /* @__PURE__ */ Object.create(null));
}
function C(e, r) {
	if (!e)
		return "";
	r = {
		encode: !0,
		strict: !0,
		arrayFormat: "none",
		arrayFormatSeparator: ",",
		...r
	}, E(r.arrayFormatSeparator);
	const t = (s) => r.skipNull && H(e[s]) || r.skipEmptyString && e[s] === "", n = B(r), a = {};
	for (const [s, o] of Object.entries(e))
		t(s) || (a[s] = o);
	const i = Object.keys(a);
	return r.sort !== !1 && i.sort(r.sort), i.map((s) => {
		const o = e[s];
		return o === void 0 ? "" : o === null ? f(s, r) : Array.isArray(o) ? o.length === 0 && r.arrayFormat === "bracket-separator" ? f(s, r) + "[]" : o.reduce(n(s), []).join("&") : f(s, r) + "=" + f(o, r);
	}).filter((s) => s.length > 0).join("&");
}
function O(e, r) {
	var a;
	r = {
		decode: !0,
		...r
	};
	let [t, n] = M(e, "#");
	return t === void 0 && (t = e), {
		url: ((a = t == null ? void 0 : t.split("?")) == null ? void 0 : a[0]) ?? "",
		query: g(h(e), r),
		...r && r.parseFragmentIdentifier && n ? { fragmentIdentifier: d(n, r) } : {}
	};
}
function _(e, r) {
	r = {
		encode: !0,
		strict: !0,
		[m]: !0,
		...r
	};
	const t = A(e.url).split("?")[0] || "", n = h(e.url), a = {
		...g(n, { sort: !1 }),
		...e.query
	};
	let i = C(a, r);
	i && (i = `?${i}`);
	let s = V(e.url);
	if (typeof e.fragmentIdentifier == "string") {
		const o = new URL(t);
		o.hash = e.fragmentIdentifier, s = r[m] ? o.hash : `#${e.fragmentIdentifier}`;
	}
	return `${t}${i}${s}`;
}
function U(e, r, t) {
	t = {
		parseFragmentIdentifier: !0,
		[m]: !1,
		...t
	};
	const { url: n, query: a, fragmentIdentifier: i } = O(e, t);
	return _({
		url: n,
		query: k(a, r),
		fragmentIdentifier: i
	}, t);
}
function K(e, r, t) {
	const n = Array.isArray(r) ? (a) => !r.includes(a) : (a, i) => !r(a, i);
	return U(e, n, t);
}
const z = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
	__proto__: null,
	exclude: K,
	extract: h,
	parse: g,
	parseUrl: O,
	pick: U,
	stringify: C,
	stringifyUrl: _
}, Symbol.toStringTag, { value: "Module" })), j = {
	BASE_URL: "https://docmee.cn"
}, G = () => j.BASE_URL, S = (e) => {
	const r = $[e], t = G();
	return t.endsWith("/") ? `${t}${r}` : `${t}/${r}`;
}, $ = {
	dashboard: "sdk-ui/dashboard",
	editor: "sdk-ui/editor",
	creator: "sdk-ui/creator/0",
	customTemplate: "sdk-ui/custom-template",
	templateCreator: "sdk-ui/custom-template-creator",
	templateMarker: "sdk-ui/marker"
};
class J {
	/**
	 *
	 * @param {DocmeeUIConstructorOptions} options
	 */
	constructor({
		token: r,
		page: t = "dashboard",
		container: n,
		pptId: a,
		onMessage: i,
		DOMAIN: s,
		...o
	}) {
		u(this, "docmeeHref", S("dashboard"));
		u(this, "query", { iframe: "1" });
		u(this, "iframe", null);
		u(this, "onMessage", () => Promise.resolve(!0));
		u(this, "iframeMounted", !1);
		u(this, "initInterval", null);
		this.onMessage = i, this.container = n, location.protocol.startsWith("file") && console.log(
			"%c %s",
			"color: red; background-color: #f7c600",
			"🔴 不能在file协议下运行，请启动一个http服务来运行！ 🔴 "
		), r || console.log(
			"%c 初始化时，token不能为空！",
			"color: #d7514f; background-color: #2e2e2e"
		), s && (j.BASE_URL = s), this.init({ token: r, page: t, pptId: a, ...o });
	}
	_postMessage(r) {
		var t, n;
		if (!this.iframe.contentWindow) throw new Error("iframe未挂载！");
		(n = (t = this.iframe) == null ? void 0 : t.contentWindow) == null || n.postMessage(r, this.docmeeHref);
	}
	init({ token: r, page: t = "dashboard", ...n }) {
		if (t === "editor" && !n.pptId)
			throw new Error("初始化editor页面时，必须传入pptId");
		this.query = Object.assign({}, this.query, n), this.docmeeHref = S(t), this.updateToken(r), this._initIframe(!0);
	}
	// 初始化iframe
	_initIframe(r) {
		const t = this.container, n = document.createElement("iframe"), a = location.href, i = z.stringifyUrl({
			url: this.docmeeHref,
			query: r ? { iframe: 1, targetOrigin: a } : this.query
		});
		n.src = i, n.style.width = "100%", n.style.height = "100%", n.style.border = "0", n.style.outline = "none", n.style.padding = "0px", n.setAttribute("allowfullscreen", "true"), this.iframe = n, this.iframeMounted = !1, t.innerHTML = "", t.appendChild(n), this.iframe.addEventListener("load", () => {
			let s = 0;
			setTimeout(() => {
				this.iframeMounted || (this.initInterval = setInterval(() => {
					if (this.iframeMounted || s >= 5)
						return s = 0, clearInterval(this.initInterval);
					r && this._postMessage({
						type: "transParams",
						data: this.query
					}), s++;
				}, 200));
			}, 300), window.addEventListener("message", async (o) => {
				var y, p;
				if (o.source !== this.iframe.contentWindow) return;
				const c = o.data;
				if (r && (c.type === "mounted" || c.type === "invalid-token") && (this.iframeMounted = !0, c.type === "mounted" && this._postMessage({
					type: "transParams",
					data: this.query
				})), c.type === "user-info" && (this.iframeMounted = !0), c.type.startsWith("before")) {
					const N = await ((y = this.onMessage) == null ? void 0 : y.call(this, c));
					this._postMessage({ data: N, type: `recover_${c.type}` });
				} else
					(p = this.onMessage) == null || p.call(this, c);
			});
		});
	}
	/**
	 * 更新用户token
	 * @param {string} latestToken 新的token
	 */
	updateToken(r) {
		/(a|s)k_.+/.test(r) || console.error("token 错误！"), this.token = r, this.query.token = r, this.iframeMounted && this._postMessage({
			type: "transParams",
			data: {
				token: r
			}
		});
	}
	/**
	 * 卸载iframe
	 */
	destroy() {
		this.container.innerHTML = "";
	}
	/**
	 * 发送消息
	 * @param {{type: 'warning' | 'success' | 'error' | 'info', content: string}}
	 */
	sendMessage(r) {
		this._postMessage({ type: "message", data: r });
	}
	getInfo() {
		this._postMessage({ type: "getInfo" });
	}
	navigate({ page: r, pptId: t, templateId: n }) {
		if (!$[r]) throw new Error(`页面${r} 不存在`);
		this._postMessage({
			type: "nav",
			data: {
				page: r,
				token: this.token,
				pptId: t,
				templateId: n
			}
		});
	}
	changeCreatorData(r, t = !1) {
		this._postMessage({
			type: "transParams",
			data: { creatorData: { ...r, createNow: t } }
		});
	}
	updateTemplate(r) {
		this._postMessage({
			type: "changeTemplateById",
			data: { templateId: r }
		});
	}
	showTemplateDialog(r = "system") {
		this._postMessage({
			type: "showTemplateDialog",
			data: { type: r }
		});
	}
	getCurrentPptInfo() {
		this._postMessage({
			type: "getCurrentPptInfo"
		});
	}
	importCSS(r) {
		this._postMessage({ type: "importCSS", data: { css: r } });
	}
}
window.DocmeeUI = J;
