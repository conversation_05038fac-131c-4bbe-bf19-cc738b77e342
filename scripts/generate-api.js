#!/usr/bin/env node

const { generateService } = require("@umijs/openapi");
const fs = require("fs");
const path = require("path");

// 从配置文件读取配置
const config = require("../api.config.js");

console.log("🚀 开始生成 API 接口文件...");
console.log(`📡 Swagger 地址: ${config.swaggerUrl}`);

// 检查是否存在本地 schema 文件
const localSchemaPath = path.resolve("./openapi.json");
const hasLocalSchema = fs.existsSync(localSchemaPath);

async function generateApi() {
	try {
		let schemaPath = config.swaggerUrl;

		// 如果存在本地文件，优先使用本地文件
		if (hasLocalSchema) {
			schemaPath = localSchemaPath;
			console.log("📁 使用本地 OpenAPI 文档: openapi.json");
		} else {
			console.log("🌐 尝试从远程获取 OpenAPI 文档...");
		}

		await generateService({
			// 从配置文件读取 Swagger 文档地址
			schemaPath,

			// 从配置文件读取生成配置
			...config.generate,
		});

		console.log("✅ API 接口文件生成完成！");
		console.log(`📁 生成位置: ${config.generate.serversPath}/`);
		console.log("📋 生成的文件包括:");
		console.log("  - typings.d.ts (TypeScript 类型定义)");
		console.log("  - [controller].ts (各个控制器的接口文件)");
		console.log("  - index.ts (统一导出)");
		console.log("");
		console.log("🎉 现在你可以这样使用:");
		console.log("  import { getUserList } from '@/services/api/user';");
		console.log("  const users = await getUserList();");
	} catch (error) {
		console.error("❌ 生成失败:", error.message);

		// 如果是网络错误，提供手动下载提示
		if (
			error.message.includes("fetch") ||
			error.message.includes("ENOTFOUND") ||
			error.message.includes("EHOSTUNREACH") ||
			error.message.includes("FetchError")
		) {
			console.log("");
			console.log("💡 网络连接失败，请手动下载 OpenAPI 文档:");
			console.log(`1. 访问: ${config.swaggerUrl}`);
			console.log(
				"2. 将返回的 JSON 内容保存为项目根目录下的 openapi.json 文件"
			);
			console.log("3. 重新运行: pnpm run openapi");
			console.log("");

			// 创建示例 openapi.json 文件
			if (!hasLocalSchema) {
				const exampleSchema = {
					openapi: "3.0.0",
					info: {
						title: "API Documentation",
						version: "1.0.0",
						description: "示例 API 文档",
					},
					servers: [
						{
							url: config.baseUrl,
							description: "开发环境",
						},
					],
					paths: {
						"/api/users": {
							get: {
								tags: ["User"],
								summary: "获取用户列表",
								operationId: "getUserList",
								parameters: [
									{
										name: "page",
										in: "query",
										description: "页码",
										schema: {
											type: "integer",
											default: 1,
										},
									},
									{
										name: "pageSize",
										in: "query",
										description: "每页数量",
										schema: {
											type: "integer",
											default: 10,
										},
									},
								],
								responses: {
									200: {
										description: "成功",
										content: {
											"application/json": {
												schema: {
													type: "object",
													properties: {
														code: {
															type: "integer",
															example: 200,
														},
														data: {
															type: "object",
															properties: {
																list: {
																	type: "array",
																	items: {
																		$ref: "#/components/schemas/User",
																	},
																},
																total: {
																	type: "integer",
																},
															},
														},
														message: {
															type: "string",
															example: "success",
														},
													},
												},
											},
										},
									},
								},
							},
							post: {
								tags: ["User"],
								summary: "创建用户",
								operationId: "createUser",
								requestBody: {
									required: true,
									content: {
										"application/json": {
											schema: {
												$ref: "#/components/schemas/CreateUserRequest",
											},
										},
									},
								},
								responses: {
									200: {
										description: "成功",
										content: {
											"application/json": {
												schema: {
													type: "object",
													properties: {
														code: {
															type: "integer",
															example: 200,
														},
														data: {
															$ref: "#/components/schemas/User",
														},
														message: {
															type: "string",
															example: "success",
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
					components: {
						schemas: {
							User: {
								type: "object",
								properties: {
									id: {
										type: "integer",
										description: "用户ID",
									},
									name: {
										type: "string",
										description: "用户名",
									},
									email: {
										type: "string",
										description: "邮箱",
									},
									createdAt: {
										type: "string",
										format: "date-time",
										description: "创建时间",
									},
								},
							},
							CreateUserRequest: {
								type: "object",
								required: ["name", "email"],
								properties: {
									name: {
										type: "string",
										description: "用户名",
									},
									email: {
										type: "string",
										description: "邮箱",
									},
								},
							},
						},
					},
				};

				fs.writeFileSync(
					localSchemaPath,
					JSON.stringify(exampleSchema, null, 2)
				);
				console.log("📝 已创建示例 openapi.json 文件");
				console.log("   请将其替换为实际的 API 文档内容，然后重新运行生成命令");
			}
		}

		process.exit(1);
	}
}

generateApi();
