{"name": "rk-web", "version": "2.0.0", "private": false, "description": "rk-web", "author": "ageer <<EMAIL>>", "keywords": ["rk-web", "chatgpt", "chatbot", "Midjourney", "Midjourney UI", "Midjourney Proxy", "gpts", "gpts ui", "vue"], "scripts": {"dev": "vite", "build": "run-p build-only", "buildold": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "common:prepare": "husky install", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "openapi": "node scripts/generate-api.js", "api:generate": "node scripts/generate-api.js"}, "dependencies": {"@tinyflow-ai/vue": "^0.0.9", "@traptitech/markdown-it-katex": "^3.6.0", "@vicons/ionicons5": "^0.13.0", "@vue-office/pdf": "^2.0.10", "@vueuse/core": "^9.13.0", "await-to-js": "^3.0.0", "echarts": "^6.0.0", "element-plus": "^2.9.2", "eventsource-parser": "^1.1.1", "form-data": "^4.0.0", "gpt-tokenizer": "^2.1.2", "highlight.js": "^11.7.0", "html2canvas": "^1.4.1", "js-audio-recorder": "^1.0.7", "katex": "^0.16.4", "localforage": "^1.10.0", "markdown-it": "^13.0.1", "naive-ui": "^2.34.3", "pinia": "^2.0.33", "vue": "^3.2.47", "vue-demi": "^0.14.10", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-waterfall-plugin-next": "^2.3.1"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@iconify/vue": "^4.1.0", "@types/crypto-js": "^4.1.1", "@types/katex": "^0.16.0", "@types/markdown-it": "^12.2.3", "@types/markdown-it-link-attributes": "^3.0.1", "@types/node": "^18.14.6", "@umijs/openapi": "^1.13.15", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "axios": "^1.3.4", "crypto-js": "^4.1.1", "http-proxy-middleware": "^2.0.6", "husky": "^8.0.3", "less": "^4.1.3", "lint-staged": "^13.1.2", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "rimraf": "^4.2.0", "svg-sprite-loader": "^3.2.6", "svgo": "^3.3.2", "tailwindcss": "^3.3.6", "typescript": "~5.9.2", "vite": "^4.2.0", "vite-plugin-pwa": "^0.14.4", "vite-plugin-svg-icons": "^2.0.1", "vitepress": "^1.6.3", "vue-tsc": "^1.2.0"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}}