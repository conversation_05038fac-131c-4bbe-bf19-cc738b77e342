server {
	listen 80;
	server_name  localhost;
	charset utf-8;
	error_page   500 502 503 504  /50x.html;
	
	# 防止爬虫抓取
	if ($http_user_agent ~* "360Spider|JikeSpider|Spider|spider|bot|Bot|2345Explorer|curl|wget|webZIP|qihoobot|Baiduspider|Googlebot|Googlebot-Mobile|Googlebot-Image|Mediapartners-Google|Adsbot-Google|Feedfetcher-Google|Yahoo! Slurp|Yahoo! Slurp China|YoudaoBot|Sosospider|Sogou spider|Sogou web spider|MSNBot|ia_archiver|Tomato Bot|NSPlayer|bingbot")
	{
		return 403;
	}
	
	location / {
			root /usr/share/nginx/html;
   		try_files $uri /index.html;
	}

	location /api {
			proxy_set_header   X-Real-IP $remote_addr; #转发用户IP
			proxy_pass http://app:3002;
	}

	proxy_set_header Host $host;
	proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header REMOTE-HOST $remote_addr;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
