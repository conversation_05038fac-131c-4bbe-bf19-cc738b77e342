import {HttpStatus} from "@/enums/RespEnum";
import cache from "@/plugins/cache";
import {getToken} from "@/store/modules/auth/helper";
import {useUserStore} from "@/store/modules/user";
import {errorCode} from "@/utils/errorCode";
import {tansParams} from "@/utils/ruoyi";
import axios, {AxiosProgressEvent, AxiosResponse, GenericAbortSignal, InternalAxiosRequestConfig,} from "axios";
import {ElMessage, ElNotification} from "element-plus";
import {createDiscreteApi} from "naive-ui";
import config from "../../../api.config";

const {message} = createDiscreteApi(["message"]);

// 统一的请求配置
const getBaseURL = () => {
	if (process.env.NODE_ENV === "development") {
		return "/api"; // 开发环境使用 Vite 代理
	}

	// 生产环境：使用相对路径
	return "";
};

// 创建统一的 axios 实例
const request = axios.create({
	baseURL: getBaseURL(),
	timeout: config.requestConfig.timeout,
	headers: {
		"Content-Type": "application/json;charset=utf-8",
		...config.requestConfig.headers,
	},
});

// 统一的请求拦截器
request.interceptors.request.use(
	(config: InternalAxiosRequestConfig) => {
		// Token 处理
		const isToken = (config.headers as any)?.isToken === false;
		if (getToken() && !isToken) {
			config.headers["Authorization"] = "Bearer " + getToken();
		}

		// GET 请求参数处理
		if (config.method === "get" && config.params) {
			let url = config.url + "?" + tansParams(config.params);
			url = url.slice(0, -1);
			config.params = {};
			config.url = url;
		}

		// 防重复提交
		const isRepeatSubmit = (config.headers as any)?.repeatSubmit === false;
		if (
			!isRepeatSubmit &&
			(config.method === "post" || config.method === "put")
		) {
			const requestObj = {
				url: config.url,
				data:
					typeof config.data === "object"
						? JSON.stringify(config.data)
						: config.data,
				time: new Date().getTime(),
			};
			const sessionObj = cache.session.getJSON("sessionObj");
			if (
				sessionObj === undefined ||
				sessionObj === null ||
				sessionObj === ""
			) {
				cache.session.setJSON("sessionObj", requestObj);
			} else {
				const s_url = sessionObj.url;
				const s_data = sessionObj.data;
				const s_time = sessionObj.time;
				const interval = 500;
				if (
					s_data === requestObj.data &&
					requestObj.time - s_time < interval &&
					s_url === requestObj.url
				) {
					const message = "数据正在处理，请勿重复提交";
					console.warn(`[${s_url}]: ` + message);
					return Promise.reject(new Error(message));
				} else {
					cache.session.setJSON("sessionObj", requestObj);
				}
			}
		}

		// FormData 处理
		if (config.data instanceof FormData) {
			delete config.headers["Content-Type"];
		}

		return config;
	},
	(error: any) => {
		console.log(error);
		return Promise.reject(error);
	}
);

// 统一的响应拦截器
request.interceptors.response.use(
	(res: AxiosResponse) => {
		// 二进制数据直接返回
		if (
			res.request.responseType === "blob" ||
			res.request.responseType === "arraybuffer"
		) {
			return res.data;
		}

		// 处理不同的响应格式
		const code = res.data.code || res.status || HttpStatus.SUCCESS;
		const msg =
			errorCode[code] ||
			res.data.msg ||
			res.data.message ||
			errorCode["default"];

		if (code === 401) {
			message.error("无效的会话，或者会话已过期，请重新登录。");
			useUserStore()
				.logout()
				.then(() => {
					location.href = "#/login";
				});
			return Promise.reject(new Error(msg));
		} else if (code === HttpStatus.SERVER_ERROR) {
			return Promise.reject(new Error(msg));
		} else if (code === HttpStatus.WARN) {
			ElMessage({
				message: msg,
				type: "warning"
			});
			return Promise.reject(new Error(msg));
		} else if (code !== HttpStatus.SUCCESS && code !== 200) {
			ElNotification.error({title: msg});
			return Promise.reject("error");
		} else {
			return Promise.resolve(res.data);
		}
	},
	(error: any) => {
		let {message} = error;
		if (message == "Network Error") {
			message = "后端接口连接异常";
		} else if (message.includes("timeout")) {
			message = "系统接口请求超时";
		} else if (message.includes("Request failed with status code")) {
			message = "系统接口" + message.substr(message.length - 3) + "异常";
		}
		ElMessage({
			message: message,
			type: "error",
			duration: 5 * 1000,
		});
		return Promise.reject(error);
	}
);

// 请求方法封装
export interface RequestOptions {
	url: string;
	method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
	data?: any;
	params?: any;
	headers?: any;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
	signal?: GenericAbortSignal;
	responseType?: "json" | "blob" | "arraybuffer";
}

export interface Response<T = any> {
	data: T;
	msg?: string;
	message?: string;
	code: number;
	rows?: any[];
}

// GET 请求
export const get = <T = any>(
	url: string,
	params?: any,
	options?: Partial<RequestOptions>
): Promise<Response<T>> => {
	return request({
		url,
		method: "GET",
		params,
		...options,
	});
};

// POST 请求
export const post = <T = any>(
	url: string,
	data?: any,
	options?: Partial<RequestOptions>
): Promise<Response<T>> => {
	return request({
		url,
		method: "POST",
		data,
		...options,
	});
};

// PUT 请求
export const put = <T = any>(
	url: string,
	data?: any,
	options?: Partial<RequestOptions>
): Promise<Response<T>> => {
	return request({
		url,
		method: "PUT",
		data,
		...options,
	});
};

// DELETE 请求
export const del = <T = any>(
	url: string,
	params?: any,
	options?: Partial<RequestOptions>
): Promise<Response<T>> => {
	return request({
		url,
		method: "DELETE",
		params,
		...options,
	});
};

// 兼容旧的调用方式
export const http = <T = any>(
	options: RequestOptions
): Promise<Response<T>> => {
	const {
		url,
		method = "GET",
		data,
		params,
		...rest
	} = options;

	if (method === "GET") {
		return request.get(url, {params: data || params, ...rest});
	} else {
		return request({
			url,
			method,
			data,
			params,
			...rest,
		});
	}
};

// 导出默认实例（兼容现有代码）
export default request;

// 导出一些常用的别名
export {request};
export const api = request;
