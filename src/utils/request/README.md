# 统一请求系统

## 简介

项目现在使用统一的请求系统，所有 API 调用都通过 `src/utils/request/index.ts` 处理。

## 使用方法

### 1. 默认导入（推荐）

```javascript
import request from "@/utils/request";

// 使用 axios 实例
const response = await request({
	url: "/api/users",
	method: "GET",
});

// 或者直接调用方法
const response = await request.get("/api/users");
const response = await request.post("/api/users", data);
```

### 2. 具名导入

```javascript
import { get, post, put, del } from "@/utils/request";

const users = await get("/api/users");
const newUser = await post("/api/users", userData);
const updatedUser = await put("/api/users/1", userData);
await del("/api/users/1");
```

### 3. 兼容旧的调用方式

```javascript
import { http } from "@/utils/request";

const response = await http({
	url: "/api/users",
	method: "GET",
	data: params,
});
```

## 环境配置

### 开发环境

- 自动代理：所有请求会自动添加 `/api` 前缀并通过 Vite 代理到后端
- 例如：`/users` → `/api/users` → 代理到后端的 `/users`

### 生产环境

- 直接请求：使用 `api.config.ts` 中配置的 `baseUrl`
- 例如：`/users` → `http://your-backend.com/users`

## 特性

- ✅ 统一的错误处理
- ✅ 自动 Token 认证
- ✅ 防重复提交
- ✅ 请求/响应拦截器
- ✅ 开发/生产环境自动适配
- ✅ TypeScript 支持
- ✅ 兼容现有代码

## 配置

所有配置都在 `api.config.ts` 中：

- `baseUrl`: 后端 API 地址
- `timeout`: 请求超时时间
- `headers`: 默认请求头

## 代理配置

Vite 配置（`vite.config.ts`）：

```javascript
proxy: {
  "^/api/": {
    target: config.baseUrl,
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, ""),
  },
}
```
