import request from "@/utils/request";

export interface ModelItem {
	modelDescribe: string;
	modelName: string;
	icon: string;
	checkYN?: boolean;
}
/**
 * 查询未隐藏模型
 * @returns
 */
export function modelList() {
	return request({
		url: "/system/model/modelList",
		method: "get",
	});
}

/**
 * 根据角色查询未隐藏模型
 * @returns
 */
export function roleModelList() {
	return request({
		url: "/system/model/roleModelList",
		method: "get",
	});
}

/**
 * 查询所有模型
 *
 * @returns
 */
export function list(category: string) {
	return request({
		url: "/system/model/list",
		method: "get",
		params: {
			category,
		},
	});
}
