<script setup lang="ts">
import {NaiveProvider} from '@/components/common'
import {useLanguage} from '@/hooks/useLanguage'
import {useTheme} from '@/hooks/useTheme'
import {NConfigProvider} from 'naive-ui'

const {
	theme,
	themeOverrides
} = useTheme()
const {language} = useLanguage()
</script>

<template>
	<NConfigProvider
		class="h-full"
		:theme="theme"
		:theme-overrides="themeOverrides"
		:locale="language"
	>
		<NaiveProvider>
			<RouterView/>
		</NaiveProvider>
	</NConfigProvider>
</template>
