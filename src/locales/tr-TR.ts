export default {
    common: {
        add: 'Ekle',
        addSuccess: '<PERSON><PERSON><PERSON> Başarılı',
        edit: '<PERSON><PERSON><PERSON><PERSON>',
        editSuccess: '<PERSON><PERSON><PERSON>leme Başarılı',
        delete: 'Sil',
        deleteSuccess: '<PERSON><PERSON><PERSON>arılı',
        save: '<PERSON><PERSON>',
        saveSuccess: '<PERSON><PERSON><PERSON> Başarılı',
        reset: 'Sıfırla',
        action: 'Eylem',
        export: 'Dışa Aktar',
        exportSuccess: 'Dışa Aktarma Başarılı',
        import: 'İçe Aktar',
        importSuccess: 'İçe Aktarma Başarılı',
        clear: 'Temizle',
        clearSuccess: 'Te<PERSON>zleme Başarılı',
        yes: 'Evet',
        no: 'Hayır',
        confirm: 'Onayla',
        download: 'İndir',
        noData: 'Veri Yok',
        wrong: 'Bir şeyler yanlış gitti, lütfen daha sonra tekrar deneyin.',
        success: '<PERSON><PERSON>ar<PERSON>l<PERSON>',
        failed: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        verify: '<PERSON>ğrula',
        unauthorizedTips: 'Yet<PERSON><PERSON>, lütfen önce doğrulama yapın.',
        stopResponding: 'Cevap Vermeyi Durdur',
    },
    chat: {
        newChatButton: 'Yeni Sohbet',
        //placeholder: 'Ask me anything...(Shift + Enter = line break, "/" to trigger prompts)',
        placeholder: 'Bana her şeyi sor, veya ekran görüntüleri yapıştır veya dosyayı sürükleyip bırak. (Shift + Enter = satır atla, "/" prompts tetiklemek için)',
        placeholderMobile: 'Bana her şeyi sor...',
        copy: 'Kopyala',
        copied: 'Kopyalandı',
        copyCode: 'Kodu Kopyala',
        clearChat: 'Sohbeti Temizle',
        clearChatConfirm: 'Bu sohbeti temizlemek istediğinizden emin misiniz?',
        exportImage: 'Resmi Dışa Aktar',
        exportImageConfirm: 'Bu sohbeti png olarak dışa aktarmak istediğinizden emin misiniz?',
        exportSuccess: 'Dışa Aktarma Başarılı',
        exportFailed: 'Dışa Aktarma Başarısız',
        usingContext: 'Bağlam Modu',
        turnOnContext: 'Mevcut modda mesaj göndermek, önceki sohbet kayıtlarını taşıyacak.',
        turnOffContext: 'Mevcut modda mesaj göndermek, önceki sohbet kayıtlarını taşımayacak.',
        deleteMessage: 'Mesajı Sil',
        deleteMessageConfirm: 'Bu mesajı silmek istediğinizden emin misiniz?',
        deleteHistoryConfirm: 'Bu geçmişi silmek istediğinizden emin misiniz?',
        clearHistoryConfirm: 'Bu sohbet geçmişini silmek istediğinizden emin misiniz?',
        preview: 'Önizleme',
        showRawText: 'Ham metin olarak göster',
    },    
    setting: {
        setting: 'Ayarlar',
        general: 'Genel',
        advanced: 'Gelişmiş',
        config: 'Yapılandırma',
        avatarLink: 'Avatar Bağlantısı',
        name: 'Ad',
        description: 'Açıklama',
        role: 'Rol',
        temperature: 'Sıcaklık',
        top_p: 'Top_p',
        resetUserInfo: 'Kullanıcı Bilgilerini Sıfırla',
        chatHistory: 'Sohbet Geçmişi',
        theme: 'Tema',
        language: 'Dil',
        api: 'API',
        reverseProxy: 'Ters Proxy',
        timeout: 'Zaman Aşımı',
        socks: 'Socks',
        httpsProxy: 'HTTPS Proxy',
        balance: 'API Bakiyesi',
        monthlyUsage: 'Aylık Kullanım',
    },       
    store: {
        siderButton: 'Prompt Mağazası',
        local: 'Yerel',
        online: 'Çevrimiçi',
        title: 'Başlık',
        description: 'Açıklama',
        clearStoreConfirm: 'Verileri silmek istediğinize emin misiniz?',
        importPlaceholder: 'Lütfen buraya JSON verilerini yapıştırın',
        addRepeatTitleTips: 'Başlık tekrarı, lütfen tekrar girin',
        addRepeatContentTips: 'İçerik tekrarı: {msg}, lütfen tekrar girin',
        editRepeatTitleTips: 'Başlık çakışması, lütfen düzeltin',
        editRepeatContentTips: 'İçerik çakışması {msg}, lütfen tekrar düzenleyin',
        importError: 'Anahtar değeri uyumsuzluk',
        importRepeatTitle: 'Başlık tekrarlı olarak atlandı: {msg}',
        importRepeatContent: 'İçerik tekrarlı olarak atlandı: {msg}',
        onlineImportWarning: 'Not: Lütfen JSON dosyası kaynağını kontrol edin!',
        downloadError: 'Lütfen ağ durumunu ve JSON dosyasının geçerliliğini kontrol edin',
    },      
    "mj": {
        "setOpen": "OpenAI İlişkilendirilmiş",
        "setOpenPlaceholder": "http(s):// içermelidir",
        "setOpenUrl": "OpenAI API Adresi",
        "setOpenKeyPlaceholder": "Parola erişim kısıtlamalarını atlamak için özel OpenAI Anahtarı kullan",
        "setMj": "Midjourney İlişkilendirilmiş",
        "setMjUrl": "Midjourney API Adresi:",
        "setMjKeyPlaceholder": "Parola erişim kısıtlamalarını atlamak için özel Api Secret kullan",
        "setUploader": "Yükleme İlişkilendirilmiş",
        "setUploaderUrl": "Yükleme Adresi:",
        "setBtSave": "Kaydet",
        "setBtBack": "Varsayılanı Geri Yükle",
    
        "redraw": "Yeniden Çiz",
        "fail1": "Lütfen sabırlı olun, yükleniyor.",
        "success1": "Resim başarıyla yenilendi!",
        "high_variation": "Güçlü Varyasyon",
        "low_variation": "Zayıf Varyasyon",
        "p15": "Zoom 1.5x",
        "p20": "Zoom 2x",
        "p100": "Normal",
        "retry": "Tekrar Dene",
        "pan_left": "Sol",
        "pan_right": "Sağ",
        "pan_up": "Yukarı",
        "pan_down": "Aşağı",
        "up2": "HD 2x",
        "up4": "HD 4x" ,
    
        "thinking": "Düşünüyor...",
        "noReUpload": "Yeniden yüklenemiyor",
        "uploading": "Yükleniyor...",
        "uploadSuccess": "Yükleme başarılı",
        "uploadFail": "Yükleme başarısız:",
        "upPdf": "<span>Resim veya ek yükleyin<br/>Resimler, PDF'ler, EXCEL ve diğer belgeleri yükleyebilirsiniz</span><p>Sürükle ve bırak desteği</p>",
        "upImg": "<span><b>Resim yükleyin</b><br/>Otomatik olarak gpt-4-vision-preview modelini çağırır<br>Not: Ek resim ücretleri uygulanabilir<br/>Formatlar: jpeg, jpg, png, gif</span><p>Sürükle ve bırak desteği</p> <p class=\"pt-2\"><b>MP3 MP4 Yükle</b> <br>Otomatik olarak whisper-1 modelini çağırır<br>Formatlar: mp3, mp4, mpeg, mpga, m4a, wav, webm</p>",
        "clearAll": "Parametreleri Temizle",
        "czoom": "Özel",
        "customTitle": "Özel zoom",
        "zoominfo": "Zoom değerini değiştirin, 1.0 ile 2.0 arasında, varsayılan 1.8 olarak ayarlanmıştır",
    
        "modleSuccess": "Model başarıyla yüklendi",
        "setingSuccess": "Ayarlar başarılı",
    
        "tokenInfo1": "Kalan Jetonlar = Model Uzunluğu - Rol Ayarı - Bağlam (Sohbet Geçmişi) - Yanıt Sayısı - Mevcut Giriş",
        "tokenInfo2": "Rol ayarı boş bırakılırsa, sistem varsayılan bir tane sağlar.",
        "noSuppertModel": "Yenile, bu model şu anda desteklenmiyor!",
        "failOcr": "Tanıma başarısız",
        "remain": "Kalan:",
    
        "totalUsage": "Toplam abonelik miktarı",
        "disableGpt4": "GPT4 devre dışı",
        "setTextInfo": "OpenAI API Anahtarı hatası, buraya tıklayarak yeniden deneyin",
    
        "attr1": "Attr",
        "ulink": "Resim Bağlantısı",
        "copyFail": "Kopyalama Başarısız",
        "tts": "Metinden Sese",
        "fail": "Hata",
        "noSupperChrom": "Tarayıcı desteklenmiyor!",
        "lang": "Ses",
        "ttsLoading": "Sese Dönüştürülüyor...",
        "ttsSuccess": "Dönüşüm başarılı",
        "micIng": "Kaydediliyor, bir şey söyle...",
        "mStart": "Başlat",
        "mPause": "Duraklat",
        "mGoon": "Devam",
        "mRecord": "Yeniden kaydet",
        "mPlay": "Oynat",
        "mCanel": "İptal",
        "mSent": "Gönder",
    
        "findVersion": "Güncellenmiş sürümü keşfet",
        "yesLastVersion": "Zaten en son sürümde",
        "infoStar": 'Bu proje <a class="text-blue-600 dark:text-blue-500" href="https://github.com/Dooy/chatgpt-web-midjourney-proxy\" target="_blank">GitHub</a> üzerinde açık kaynaklı, ücretsiz ve MIT lisansına dayanmaktadır, herhangi bir ödeme şekli yoktur! </p><p>Bu projeyi yararlı bulursanız, lütfen GitHub üzerinde yıldız verin, teşekkür ederim!',
        "setBtSaveChat": "Sadece sohbeti kaydet",
        "setBtSaveSys": "Sisteme kaydet",
        "wsrvClose": "wsrv'yi kapat",
        "wsrvOpen": "wsrv'yi aç",
        
        "temperature": "Rastlantısallık",
        "temperatureInfo": "(temperature) değeri arttıkça yanıtlar daha rastlantısal hale gelir",
        "top_p": "Üst Olasılık Örnekleme",
        "top_pInfo": "(top_p) rastlantısallığa benzer ancak sıcaklık ile birlikte değiştirilmemelidir",
        "presence_penalty": "Konu Tazeliği",
        "presence_penaltyInfo": "(presence_penalty) değeri arttıkça, yeni konulara genişleme olasılığı daha yüksektir",
        "frequency_penalty": "Frekans Cezası",
        "frequency_penaltyInfo": "(frequency_penalty) değeri arttıkça, tekrarlanan kelimelerin azaltılma olasılığı daha yüksektir"
        ,"tts_voice": "TTS Ses Karakteri",
        "typing": "Yazıyor",
        "authErro": "Yetkilendirme başarısız", 
        "authBt": "Lütfen yetkilendirme erişim şifresini yeniden girin",
        "micWhisper": "Fısıltı konuşma tanıma",
        "micAsr": "Anında tanıma",
        "micRec": "Kayıt başlat, lütfen konuşun! 2 saniye boyunca ses yoksa otomatik olarak duracaktır.",
        "micRecEnd": "Kayıt sona erdi"
    },        
    "mjset": {
        "server": "Sunucu",
        "about": "Hakkında",
        "model": "Model",
        "sysname": "Yapay Zeka Çizimi"
    },
    "mjtab": {
        "chat": "Sohbet",
        "draw": "Çizim",
        "drawinfo": "Midjourney Motoru ile Yapay Zeka Çizimi",
        "gallery": "Galeri",
        "galleryInfo": "Benim Galerim"
    },        
    "mjchat": {
        "loading": "Resim Yükleniyor",
        "openurl": "Bağlantıyı Doğrudan Aç",
        "failReason": "Başarısızlık Nedeni:",
        "reload": "Yeniden Yükle",
        "progress": "İlerleme:",
        "wait": "Görev gönderildi, lütfen bekleyin...",
        "reroll": "Yeniden Çiz",
        "wait2": "Görev {id} gönderildi, lütfen bekleyin",
        "redrawEditing": "Kısmi Yeniden Çiz Düzenleme",
        "face": "Yüz Değiştir",
        "blend": "Resimleri Karıştır",
        "draw": "Çizim",
        "submiting": "Gönderiliyor",
        "submit": "Gönder",
        "wait3": "Lütfen kapatmayın! Resim oluşturuluyor...",
        "success": "Başarıyla Kaydedildi",
        "successTitle": "Başarı",
        "modlePlaceholder": "Özel modeller, boşluklarla ayrılmış (isteğe bağlı)",
        "myModle": "Özel Modeller",
        "historyCnt": "Bağlam Sayısı",
        "historyToken": "Daha fazla bağlam, doğruluğu artırır ancak daha fazla kredi tüketir",
        "historyTCnt": "Yanıt Sayısı",
        "historyTCntInfo": "Daha yüksek yanıt sayısı daha fazla kredi tüketebilir",
        "role": "Rol Ayarı",
        "rolePlaceholder": "Konuşmanız için özel bir rol ayarlayın (isteğe bağlı)",
        "loading2": "Yükleniyor...",
        "loadmore": "Daha Fazla Yükle",
        "nofind": "Bulunamadı",
        "nofind2": "ilgili içerik. Aşağıdakileri deneyebilirsiniz:",
        "success2": "Değiştirme Başarılı!",
        "modelChange": "Model Değişikliği",
        "search": "Ara",
        "searchPlaceholder": "GPT isimleri, açıklamalar",
        "attr": "Ekler",
        "noproduct": "Galari henüz giriş yapmamış",
        "myGallery": "Benim Galerim",
        "yourHead": "Senin Avatarın",
        "your2Head": "Ünlü Resim",
        "tipInfo": "Not:<li>1. Resimlerin uygun bir şekilde oluşturulması için yüz içermesi gerekir</li><li>2. 'Ünlü Resim', MJ çizimi kullanılarak oluşturulabilir</li><li>3. 'Ünlü Resim', aynı zamanda anime karakterlerini içerebilir</li><li>4. 'Senin Avatarın', bir pasaport fotoğrafı boyutunda olması önerilir</li>",
        "placeInput": "Lütfen prompt'u doldurun!",
        "more5sb": "En fazla 5 resim yükleyin",
        "exSuccess": "Başarıyla Dışa Aktar... İndirme klasörünü kontrol edin",
        "downloadSave": "ai_drawing.txt",
        "noproducet": "Şu anda olgun çalışma yok",
        "imgBili": "Resim Oranı",
        "imagEx": "Sanat Eseri Resim Bağlantılarını Dışa Aktar",
        "prompt": "Prompts",
        "imgCYes": "Temel Resim İçerir",
        "imgCUpload": "Temel Resim Yükle",
        "imgCInfo": "Temel Resim Bilgisi:<br/>1. MJ çizimi için kendi resimlerinizi temel olarak kullanın<br/>2. Birden fazla temel resim kullanabilirsiniz, her biri 1M boyutunu geçmemeli",
        "imgCadd": "+Ekle",
        "del": "Sil",
        "img2text": "Resimden Metne",
        "img2textinfo": "Hangi prompts kullanacağınızdan emin değil misiniz? Resimden Metne'yi deneyin! Bir resim göndererek prompts alın",
        "traning": "Çevriliyor...",
        "imgcreate": "Resim Oluştur",
        "imginfo": "Diğer parametreler:<li>1 --no: Resimden arabaları hariç tutmak için --no car'ı yoksayın</li><li>2 --seed: --seed 123456 ile önce bir tohum alın</li><li>3 --chaos 10: Karıştırma (aralık: 0-100)</li><li>4 --tile: Parçalama</li>",
        "tStyle": "Stil",
        "tView": "Görünüm",
        "tShot": "Karakter Çekimi",
        "tLight": "Aydınlatma",
        "tQuality": "Resim Kalitesi",
        "tStyles": "Sanatsal Seviye",
        "tVersion": "Model Sürümü",
        "dalleInfo": "Not:<li>1. DALL-E, OpenAI tarafından sağlanan bir resim oluşturma modelidir</li><li>2. OpenAI resimlerinin bir son kullanma tarihi vardır, bu nedenle yedekleme yapın</li><li>3. Not: 1790px resimlerin fiyatı iki katıdır</li>",
        "version": "Sürüm",
        "size": "Boyut",
        "blendInfo": "Not:<li>1. En az 2 resmi karıştırın</li><li>2. Karıştırmak için en fazla 6 resim kullanılabilir</li>",
        "blendStart": "Karışımı Başlat",
        "no2add": "Çift resim ekleme",
        "add2more": "Lütfen iki veya daha fazla resim ekleyin",
        "no1m": "Resim boyutu 1M'yi aşamaz",
        "imgExt": "Resimler sadece jpg, gif, png, jpeg formatlarını destekler"
    }
    
  }
  