export default {
  common: {
    add: 'Thêm',
    addSuccess: 'Thêm thành công',
    edit: 'Sửa',
    editSuccess: 'Sửa thành công',
    delete: 'Xóa',
    deleteSuccess: '<PERSON><PERSON><PERSON> thành công',
    save: '<PERSON><PERSON><PERSON>',
    saveSuccess: '<PERSON><PERSON><PERSON> thành công',
    reset: 'Đặt lại',
    action: 'Hành động',
    export: 'Xuất',
    exportSuccess: 'Xuất thành công',
    import: 'Nhập',
    importSuccess: 'Nhập thành công',
    clear: 'Dọn dẹp',
    clearSuccess: 'Dọn dẹp thành công',
    yes: 'Có',
    no: 'Không',
    confirm: '<PERSON>ác nhận',
    download: 'Tải xuống',
    noData: 'Không có dữ liệu',
    wrong: 'Đã xảy ra lỗi, vui lòng thử lại sau.',
    success: 'Thành công',
    failed: 'Thất bại',
    verify: '<PERSON><PERSON><PERSON> minh',
    unauthorizedTips: '<PERSON>h<PERSON><PERSON> được <PERSON> quyền, vui lòng xác minh trước.',
  },
  chat: {
    newChatButton: 'Tạo hội thoại',
    placeholder: 'Hỏi tôi bất cứ điều gì...(Shift + Enter = ngắt dòng, "/" to trigger prompts)',
    placeholderMobile: 'Hỏi tôi bất cứ điều gì...',
    copy: 'Sao chép',
    copied: 'Đã sao chép',
    copyCode: 'Sao chép Code',
    clearChat: 'Clear Chat',
    clearChatConfirm: 'Bạn có chắc chắn xóa cuộc trò chuyện này?',
    exportImage: 'Xuất hình ảnh',
    exportImageConfirm: 'Bạn có chắc chắn xuất cuộc trò chuyện này sang png không?',
    exportSuccess: 'Xuất thành công',
    exportFailed: 'Xuất thất bại',
    usingContext: 'Context Mode',
    turnOnContext: 'Ở chế độ hiện tại, việc gửi tin nhắn sẽ mang theo các bản ghi trò chuyện trước đó.',
    turnOffContext: 'Ở chế độ hiện tại, việc gửi tin nhắn sẽ không mang theo các bản ghi trò chuyện trước đó.',
    deleteMessage: 'Xóa tin nhắn',
    deleteMessageConfirm: 'Bạn có chắc chắn xóa tin nhắn này?',
    deleteHistoryConfirm: 'Bạn có chắc chắn để xóa lịch sử này?',
    clearHistoryConfirm: 'Bạn có chắc chắn để xóa lịch sử trò chuyện?',
    preview: 'Xem trước',
    showRawText: 'Hiển thị dưới dạng văn bản thô',
  },
  setting: {
    setting: 'Cài đặt',
    general: 'Chung',
    advanced: 'Nâng cao',
    config: 'Cấu hình',
    avatarLink: 'Avatar Link',
    name: 'Tên',
    description: 'Miêu tả',
    role: 'Vai trò',
    temperature: 'Nhiệt độ',
    top_p: 'Top_p',
    resetUserInfo: 'Đặt lại thông tin người dùng',
    chatHistory: 'Lịch sử trò chuyện',
    theme: 'Giao diện',
    language: 'Ngôn ngữ',
    api: 'API',
    reverseProxy: 'Reverse Proxy',
    timeout: 'Timeout',
    socks: 'Socks',
    httpsProxy: 'HTTPS Proxy',
    balance: 'API Balance',
    monthlyUsage: 'Sử dụng hàng tháng',
  },
  store: {
    siderButton: 'Prompt Store',
    local: 'Local',
    online: 'Online',
    title: 'Tiêu đề',
    description: 'Miêu tả',
    clearStoreConfirm: 'Cho dù để xóa dữ liệu?',
    importPlaceholder: 'Vui lòng dán dữ liệu JSON vào đây',
    addRepeatTitleTips: 'Tiêu đề trùng lặp, vui lòng nhập lại',
    addRepeatContentTips: 'Nội dung trùng lặp: {msg}, vui lòng nhập lại',
    editRepeatTitleTips: 'Xung đột tiêu đề, vui lòng sửa lại',
    editRepeatContentTips: 'Xung đột nội dung {msg} , vui lòng sửa đổi lại',
    importError: 'Key value mismatch',
    importRepeatTitle: 'Tiêu đề liên tục bị bỏ qua: {msg}',
    importRepeatContent: 'Nội dung liên tục bị bỏ qua: {msg}',
    onlineImportWarning: 'Lưu ý: Vui lòng kiểm tra nguồn tệp JSON!',
    downloadError: 'Vui lòng kiểm tra trạng thái mạng và tính hợp lệ của tệp JSON',
  },
  "mj": {
    "setOpen": "OpenAI liên quan",
    "setOpenPlaceholder": "Phải chứa http(s)://",
    "setOpenUrl": "Địa chỉ giao diện OpenAI",
    "setOpenKeyPlaceholder": "Sử dụng khóa OpenAI tùy chỉnh để bỏ qua hạn chế mật khẩu",
    "setMj": "Midjourney liên quan",
    "setMjUrl": "Địa chỉ giao diện Midjourney:",
    "setMjKeyPlaceholder": "Sử dụng Khóa Api Secret tùy chỉnh để bỏ qua hạn chế mật khẩu",
    "setUploader": "Tải lên liên quan",
    "setUploaderUrl": "Địa chỉ tải lên:",
    "setBtSave": "Lưu",
    "setBtBack": "Khôi phục mặc định",

    "redraw": "Vẽ Lại Phần",
    "fail1": "Anh/chị đừng vội, đang tải đó.",
    "success1": "Ảnh đã làm mới thành công!",
    "high_variation": "Biến Động Mạnh",
    "low_variation": "Biến Động Nhẹ",
    "p15": "Thu Phóng 1.5 lần",
    "p20": "Thu Phóng 2 lần",
    "p100": "Bình thường",
    "retry": "Thử Lại Phân Tích",
    "pan_left": "Phân Tích Lại Bên Trái",
    "pan_right": "Phân Tích Lại Bên Phải",
    "pan_up": "Phân Tích Lại Lên",
    "pan_down": "Phân Tích Lại Xuống",
    "up2": "Độ Phân Giải Cao 2 lần",
    "up4": "Độ Phân Giải Cao 4 lần",

    "thinking": "Đang suy nghĩ...",
    "noReUpload": "Không thể tải lên lại",
    "uploading": "Đang tải lên...",
    "uploadSuccess": "Tải lên thành công",
    "uploadFail": "Tải lên thất bại:",
    "upPdf": "<span>Tải lên hình ảnh hoặc tệp đính kèm<br/>Bạn có thể tải lên hình ảnh, PDF, EXCEL và các tài liệu khác</span><p>Hỗ trợ kéo và thả</p>",
    "upImg": "<span><b>Tải lên hình ảnh</b><br/>Sẽ tự động gọi mô hình gpt-4-vision-preview<br>Chú ý: Có thể áp dụng phí ảnh bổ sung<br/>Định dạng: jpeg, jpg, png, gif</span><p>Hỗ trợ kéo và thả</p> <p class=\"pt-2\"><b>Tải lên MP3 MP4</b> <br>Sẽ tự động gọi mô hình whisper-1<br>Định dạng: mp3, mp4, mpeg, mpga, m4a, wav, webm</p>",
    "clearAll": "Xóa tất cả các tham số",
    "czoom": "Tùy chỉnh",
    "customTitle": "Tùy chỉnh zoom",
    "zoominfo": "Sửa giá trị zoom, khoảng từ 1.0 đến 2.0, mặc định được đặt là 1.8",

    "modleSuccess": "Tải mô hình thành công",
    "setingSuccess": "Thiết lập thành công",

    "tokenInfo1": "Còn lại Tokens = Độ dài mô hình - Thiết lập vai trò - Bối cảnh (Lịch sử cuộc trò chuyện) - Số phản hồi - Đầu vào hiện tại",
    "tokenInfo2": "Để trống thiết lập vai trò và hệ thống sẽ cung cấp một giá trị mặc định.",
    "noSuppertModel": "Làm mới, hiện tại mô hình này không được hỗ trợ!",
    "failOcr": "Nhận dạng thất bại",
    "remain": "Còn:",

    "totalUsage": "Tổng số tiền đăng ký",
    "disableGpt4": "GPT4 đã tắt",
    "setTextInfo": "Lỗi Khóa API OpenAI, nhấp vào đây để thử lại" ,

    "attr1": "Đính",
    "ulink": "Liên kết Ảnh gốc",
    "copyFail": "Sao chép thất bại",
    "tts": "Văn bản thành Tiếng nói (TTS)",
    "fail": "Đã xảy ra lỗi",
    "noSupperChrom": "Trình duyệt không được hỗ trợ!",
    "lang": "Âm thanh",
    "ttsLoading": "Đang chuyển đổi thành tiếng nói...",
    "ttsSuccess": "Chuyển đổi thành công",
    "micIng": "Đang ghi âm, nói điều gì đó...",
    "mStart": "Bắt đầu",
    "mPause": "Tạm dừng",
    "mGoon": "Tiếp tục",
    "mRecord": "Ghi lại",
    "mPlay": "Phát",
    "mCanel": "Hủy",
    "mSent": "Gửi",
    "findVersion": "Phát hiện phiên bản cập nhật",
    "yesLastVersion": "Đã là phiên bản mới nhất",
    "infoStar": "Dự án này được mở nguồn tại <a class=\"text-blue-600 dark:text-blue-500\" href=\"https://github.com/Dooy/chatgpt-web-midjourney-proxy\" target=\"_blank\">GitHub</a>, miễn phí và dựa trên giấy phép MIT mà không có bất kỳ hình thức thanh toán nào! </p><p>Nếu bạn thấy dự án này hữu ích, hãy cho nó một sao trên GitHub, cảm ơn bạn!",
    "setBtSaveChat": "Chỉ lưu trò chuyện",
    "setBtSaveSys": "Lưu vào hệ thống",

    "wsrvClose": "Đóng wsrv",
    "wsrvOpen": "Mở wsrv",
    
    "temperature": "Ngẫu nhiên",
    "temperatureInfo": "Khi giá trị (temperature) tăng, các phản hồi trở nên ngẫu nhiên hơn",
    "top_p": "Lấy Mẫu Xác Suất Cao Nhất",
    "top_pInfo": "(top_p) tương tự như ngẫu nhiên nhưng không nên thay đổi cùng với nhiệt độ",
    "presence_penalty": "Sự Tươi Mới của Chủ đề",
    "presence_penaltyInfo": "Khi giá trị (presence_penalty) tăng, có khả năng mở rộng đến các chủ đề mới cao hơn",
    "frequency_penalty": "Hình Phạt Tần Số",
    "frequency_penaltyInfo": "Khi giá trị (frequency_penalty) tăng, có khả năng giảm sự lặp lại của các từ nhiều hơn"
   ,"tts_voice": "Nhân vật giọng TTS",
    "typing": "Đang nhập",
    "authErro": "Xác thực không thành công",
    "authBt": "Vui lòng nhập lại mật khẩu truy cập xác thực",

    "micWhisper": "Nhận diện giọng nói thì thầm",
    "micAsr": "Nhận diện ngay lập tức",
    "micRec": "Bắt đầu ghi âm, vui lòng nói chuyện! Sẽ tự động dừng nếu không có âm thanh trong vòng 2 giây.",
    "micRecEnd": "Ghi âm đã kết thúc"

  },
  "mjset": {
    "server": "Máy chủ",
    "about": "Về",
    "model": "Mô hình",
    "sysname": "Trí tuệ nhân tạo vẽ"
  },
  "mjtab": {
    "chat": "nói",
    "draw": "Vẽ",
    "drawinfo": "Vẽ trí tuệ nhân tạo Midjourney",
    "gallery": "sách",
    "galleryInfo": "Phòng trưng bày của tôi"
  },
  "mjchat": {
    "loading": "Đang tải hình ảnh",
    "openurl": "Mở liên kết trực tiếp",
    "failReason": "Lý do thất bại:",
    "reload": "Tải lại",
    "progress": "Tiến triển:",
    "wait": "Nhiệm vụ đã được gửi, vui lòng đợi...",
    "reroll": "Vẽ lại",
    "wait2": "Nhiệm vụ {id} đã được gửi, vui lòng đợi",
    "redrawEditing": "Chỉnh sửa vẽ lại",
    "face": "Thay đổi khuôn mặt",
    "blend": "Trộn ảnh",
    "draw": "Vẽ",
    "submiting": "Đang gửi",
    "submit": "Gửi",
    "wait3": "Vui lòng không tắt! Đang tạo ảnh...",
    "success": "Lưu thành công",
    "successTitle": "Thành công",
    "modlePlaceholder": "Mô hình tùy chỉnh, cách nhau bằng khoảng trắng, không bắt buộc",
    "myModle": "Mô hình tùy chỉnh của tôi",
    "historyCnt": "Số ngữ cảnh",
    "historyToken": "Số ngữ cảnh nhiều hơn sẽ làm cho bộ nhớ chính xác hơn, nhưng sẽ tiêu tốn nhiều chi phí hơn",
    "historyTCnt": "Số câu trả lời",
    "historyTCntInfo": "Số câu trả lời càng nhiều, khả năng tiêu tốn chi phí càng cao",
    "role": "Đặt vai trò",
    "rolePlaceholder": "Đặt một vai trò riêng cho cuộc trò chuyện của bạn, không bắt buộc",
    "loading2": "Đang tải...",
    "loadmore": "Tải thêm",
    "nofind": "Không thể tìm thấy",
    "nofind2": "Nội dung liên quan không tìm thấy, bạn có thể thử những nội dung sau đây",
    "success2": "Chuyển đổi thành công!",
    "modelChange": "Thay đổi mô hình",
    "search": "Tìm kiếm",
    "searchPlaceholder": "Tên GPTs, giới thiệu",
    "attr": "Phụ kiện",
    "noproduct": "Phòng trưng bày chưa có sản phẩm của bạn",
    "myGallery": "Phòng trưng bày của tôi",
    "yourHead": "Ảnh đại diện của bạn",
    "your2Head": "Ảnh ngôi sao",
    "tipInfo": "Chú ý: <li>1 Hình ảnh phải chứa khuôn mặt, nếu không sẽ không xuất hiện ảnh</li> <li>2 'Ảnh ngôi sao' có thể sử dụng MJ để vẽ trước</li> <li>3 'Ảnh ngôi sao' có thể là hình ảnh hoạt hình</li> <li>4 'Ảnh đại diện của bạn' nên sử dụng ảnh cá nhân 1 inch</li>",
    "placeInput": "Vui lòng điền từ gợi ý!",
    "more5sb": "Tối đa tải lên 5 ảnh",
    "exSuccess": "Xuất thành công... Vui lòng kiểm tra thanh tải về",
    "downloadSave": "ai vẽ.txt",
    "noproducet": "Hiện chưa có sản phẩm chín thành",
    "imgBili": "Tỉ lệ ảnh",
    "imagEx": "Xuất liên kết hình ảnh tác phẩm",
    "prompt": "Từ gợi ý",
    "imgCYes": "Có ảnh nền",
    "imgCUpload": "Tự tải ảnh nền",
    "imgCInfo": "Thông tin ảnh nền: <br/> 1. Ảnh nền có thể sử dụng ảnh cá nhân của bạn làm cơ sở để MJ vẽ hình <br/> 2. Có thể sử dụng nhiều ảnh nền, tối đa 5 ảnh, mỗi ảnh không quá 1M",
    "imgCadd": "+ Thêm",
    "del": "Xóa",
    "img2text": "Hình thành văn",
    "img2textinfo": "Không biết cách đặt từ gợi ý? Hãy thử Hình thành văn! <br/> Gửi hình ảnh, nhận từ gợi ý",
    "traning": "Đang dịch...",
    "imgcreate": "Tạo ảnh",
    "imginfo": "Tham số khác: <li>1 --no Bỏ qua --no, không xuất hiện xe trong hình ảnh </li><li>2 --seed Có thể lấy hạt giống trước --seed 123456 </li> <li>3 --chaos 10 Hỗn loạn (phạm vi: 0-100)</li> <li>4 --tile Fragmentation </li>",
    "tStyle": "Phong cách",
    "tView": "Góc nhìn",
    "tShot": "Góc chụp người",
    "tLight": "Ánh sáng",
    "tQuality": "Chất lượng hình ảnh",
    "tStyles": "Mức độ nghệ thuật",
    "tVersion": "Phiên bản mô hình",
    "dalleInfo": "Chú ý: <li>1 Dall-e là mô hình vẽ hình do OpenAI cung cấp</li>  <li>2 Hình ảnh của OpenAI có thời gian sử dụng, hãy sao lưu đúng cách</li>   <li>3 Lưu ý: Giá của hình ảnh 1790px là gấp đôi</li>",
    "version": "Phiên bản",
    "size": "Kích thước",
    "blendInfo": "Chú ý: <li>1 Kết hợp ít nhất 2 hình ảnh</li> <li>2 Tối đa có thể tải lên 6 hình ảnh</li>",
    "blendStart": "Bắt đầu kết hợp",
    "no2add": "Vui lòng không thêm hình ảnh giống nhau",
    "add2more": "Vui lòng thêm ít nhất hai hình ảnh",
    "no1m": "Kích thước hình ảnh không quá 1M",
    "imgExt": "Chỉ hỗ trợ định dạng jpg, gif, png, jpeg cho hình ảnh"
    }
}
