import type { GlobalThemeOverrides } from "naive-ui";
import { computed, watch } from "vue";
import { darkTheme, useOsTheme } from "naive-ui";
import { useAppStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";

export function useTheme() {
	const appStore = useAppStore();

	const OsTheme = useOsTheme();

	const isDark = computed(() => {
		if (appStore.theme === "auto") return OsTheme.value === "dark";
		else return appStore.theme === "dark";
	});

	const theme = computed(() => {
		return isDark.value ? darkTheme : undefined;
	});

	const themeOverrides = computed<GlobalThemeOverrides>(() => {
		const blueTheme = {
			common: {
				primaryColor: "#1890ff",
				primaryColorHover: "#40a9ff",
				primaryColorPressed: "#096dd9",
				primaryColorSuppl: "#1890ff",
			},
			Button: {
				textColorPrimary: "#ffffff",
				colorPrimary: "#1890ff",
				colorHoverPrimary: "#40a9ff",
				colorPressedPrimary: "#096dd9",
				colorFocusPrimary: "#40a9ff",
			},
			Input: {
				borderHover: "#40a9ff",
				borderFocus: "#1890ff",
				boxShadowFocus: "0 0 0 2px rgba(24, 144, 255, 0.2)",
			},
			Select: {
				peers: {
					InternalSelection: {
						borderHover: "#40a9ff",
						borderActive: "#1890ff",
						boxShadowActive: "0 0 0 2px rgba(24, 144, 255, 0.2)",
					},
				},
			},
			Radio: {
				buttonColorActive: "#1890ff",
				buttonTextColorActive: "#ffffff",
				buttonColorHover: "#40a9ff",
				buttonBorderColorActive: "#1890ff",
				buttonBorderColorHover: "#40a9ff",
			},
		};

		if (isDark.value) {
			return {
				...blueTheme,
				common: {
					...blueTheme.common,
				},
			};
		}
		return blueTheme;
	});
	const { isMobile } = useBasicLayout();
	watch(
		() => isDark.value,
		(dark) => {
			if (dark) document.documentElement.classList.add("dark");
			else document.documentElement.classList.remove("dark");
		},
		{ immediate: true }
	);
	watch(
		() => isMobile.value,
		(dark) => {
			if (dark) document.documentElement.classList.add("is-mobile");
			else document.documentElement.classList.remove("is-mobile");
		},
		{ immediate: true }
	);

	return { theme, themeOverrides };
}
