// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 此处后端没有提供注释 GET /mj/task/${param0}/fetch */
export async function fetch(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fetchParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/mj/task/${param0}/fetch`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /mj/task/${param0}/image-seed */
export async function getSeed(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSeedParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/mj/task/${param0}/image-seed`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/task/list-by-condition */
export async function listByIds(
  body: API.TaskConditionDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/task/list-by-condition", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
