// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改聊天消息 修改聊天消息 PUT /system/message */
export async function edit9(
  body: API.ChatMessageBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/message", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增聊天消息 新增聊天消息 POST /system/message */
export async function add9(
  body: API.ChatMessageBo,
  options?: { [key: string]: any }
) {
  return request<API.RLong>("/system/message", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取聊天消息详细信息 获取聊天消息详细信息 GET /system/message/${param0} */
export async function getInfo12(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo12Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RChatMessageVo>(`/system/message/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除聊天消息 删除聊天消息 DELETE /system/message/${param0} */
export async function remove13(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove13Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/message/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出聊天消息列表 导出聊天消息列表 POST /system/message/export */
export async function export8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export8Params,
  options?: { [key: string]: any }
) {
  return request<any>("/system/message/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询聊天消息列表 查询聊天消息列表 GET /system/message/list */
export async function list9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list9Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoChatMessageVo>("/system/message/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}
