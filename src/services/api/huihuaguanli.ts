// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改会话管理 修改会话管理 PUT /system/session */
export async function edit2(
  body: API.ChatSessionBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/session", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增会话管理 新增会话管理 POST /system/session */
export async function add2(
  body: API.ChatSessionBo,
  options?: { [key: string]: any }
) {
  return request<API.RLong>("/system/session", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取会话管理详细信息 获取会话管理详细信息 GET /system/session/${param0} */
export async function getInfo5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo5Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RChatSessionVo>(`/system/session/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除会话管理 删除会话管理 DELETE /system/session/${param0} */
export async function remove6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove6Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/session/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出会话管理列表 导出会话管理列表 POST /system/session/export */
export async function export2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export2Params,
  options?: { [key: string]: any }
) {
  return request<any>("/system/session/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询会话管理列表 查询会话管理列表 GET /system/session/list */
export async function list2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list2Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoChatSessionVo>("/system/session/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}
