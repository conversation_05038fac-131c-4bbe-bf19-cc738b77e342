// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 邮件登录 邮件登录 POST /auth/emailLogin */
export async function emailLogin(
  body: API.EmailLoginBody,
  options?: { [key: string]: any }
) {
  return request<API.RLoginVo>("/auth/emailLogin", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 登录方法 登录方法 POST /auth/login */
export async function login(
  body: API.LoginBody,
  options?: { [key: string]: any }
) {
  return request<API.RLoginVo>("/auth/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 退出登录 退出登录 POST /auth/logout */
export async function logout(options?: { [key: string]: any }) {
  return request<API.RVoid>("/auth/logout", {
    method: "POST",
    ...(options || {}),
  });
}

/** 用户注册 用户注册 POST /auth/register */
export async function register(
  body: API.RegisterBody,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/auth/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 重置密码 重置密码 POST /auth/reset/password */
export async function resetPassWord(
  body: API.RegisterBody,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/auth/reset/password", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 短信登录 短信登录 POST /auth/smsLogin */
export async function smsLogin(
  body: API.SmsLoginBody,
  options?: { [key: string]: any }
) {
  return request<API.RLoginVo>("/auth/smsLogin", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 登录页面租户下拉框 登录页面租户下拉框 GET /auth/tenant/list */
export async function tenantList(options?: { [key: string]: any }) {
  return request<API.RLoginTenantVo>("/auth/tenant/list", {
    method: "GET",
    ...(options || {}),
  });
}

/** 访客登录 访客登录 POST /auth/visitorLogin */
export async function visitorLogin(
  body: API.VisitorLoginBody,
  options?: { [key: string]: any }
) {
  return request<API.RLoginVo>("/auth/visitorLogin", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
