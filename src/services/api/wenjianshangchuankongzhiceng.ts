// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 删除OSS对象存储 删除OSS对象存储 DELETE /resource/oss/${param0} */
export async function remove17(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove17Params,
  options?: { [key: string]: any }
) {
  const { ossIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/resource/oss/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 下载OSS对象 下载OSS对象 GET /resource/oss/download/${param0} */
export async function download1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.download1Params,
  options?: { [key: string]: any }
) {
  const { ossId: param0, ...queryParams } = params;
  return request<any>(`/resource/oss/download/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询OSS对象存储列表 查询OSS对象存储列表 GET /resource/oss/list */
export async function list15(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list15Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysOssVo>("/resource/oss/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 查询OSS对象基于id串 查询OSS对象基于id串 GET /resource/oss/listByIds/${param0} */
export async function listByIds1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listByIds1Params,
  options?: { [key: string]: any }
) {
  const { ossIds: param0, ...queryParams } = params;
  return request<API.RListSysOssVo>(`/resource/oss/listByIds/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 上传OSS对象存储 上传OSS对象存储 POST /resource/oss/upload */
export async function upload(
  body: {},
  file?: File,
  options?: { [key: string]: any }
) {
  const formData = new FormData();

  if (file) {
    formData.append("file", file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === "object" && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ""));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.RSysOssUploadVo>("/resource/oss/upload", {
    method: "POST",
    data: formData,
    requestType: "form",
    ...(options || {}),
  });
}
