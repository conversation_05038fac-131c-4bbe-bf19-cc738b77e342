// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改支付订单 修改支付订单 PUT /system/payOrder */
export async function edit5(
  body: API.ChatPayOrderBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/payOrder", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增支付订单 新增支付订单 POST /system/payOrder */
export async function add5(
  body: API.ChatPayOrderBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/payOrder", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取支付订单详细信息 获取支付订单详细信息 GET /system/payOrder/${param0} */
export async function getInfo8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo8Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RChatPayOrderVo>(`/system/payOrder/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除支付订单 删除支付订单 DELETE /system/payOrder/${param0} */
export async function remove9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove9Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/payOrder/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出支付订单列表 导出支付订单列表 POST /system/payOrder/export */
export async function export5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export5Params,
  options?: { [key: string]: any }
) {
  return request<any>("/system/payOrder/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询支付订单列表 查询支付订单列表 GET /system/payOrder/list */
export async function list5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list5Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoChatPayOrderVo>("/system/payOrder/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}
