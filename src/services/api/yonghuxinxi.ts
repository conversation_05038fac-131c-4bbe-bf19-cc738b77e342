// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改用户 修改用户 PUT /system/user */
export async function edit(
  body: API.SysUserBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增用户 新增用户 POST /system/user */
export async function add(
  body: API.SysUserBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据用户编号获取详细信息 根据用户编号获取详细信息 GET /system/user/ */
export async function getInfo2(options?: { [key: string]: any }) {
  return request<API.RSysUserInfoVo>("/system/user/", {
    method: "GET",
    ...(options || {}),
  });
}

/** 根据用户编号获取详细信息 根据用户编号获取详细信息 GET /system/user/${param0} */
export async function getInfo3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo3Params,
  options?: { [key: string]: any }
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.RSysUserInfoVo>(`/system/user/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除用户 删除用户 DELETE /system/user/${param0} */
export async function remove4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove4Params,
  options?: { [key: string]: any }
) {
  const { userIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/user/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 用户授权角色 用户授权角色 PUT /system/user/authRole */
export async function insertAuthRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.insertAuthRoleParams,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user/authRole", {
    method: "PUT",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据用户编号获取授权角色 根据用户编号获取授权角色 GET /system/user/authRole/${param0} */
export async function authRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.authRoleParams,
  options?: { [key: string]: any }
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.RSysUserInfoVo>(`/system/user/authRole/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 状态修改 状态修改 PUT /system/user/changeStatus */
export async function changeStatus(
  body: API.SysUserBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user/changeStatus", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门树列表 获取部门树列表 GET /system/user/deptTree */
export async function deptTree(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deptTreeParams,
  options?: { [key: string]: any }
) {
  return request<API.RList>("/system/user/deptTree", {
    method: "GET",
    params: {
      ...params,
      dept: undefined,
      ...params["dept"],
    },
    ...(options || {}),
  });
}

/** 修改用户头像 修改用户头像 POST /system/user/edit/avatar */
export async function editAvatar(body: {}, options?: { [key: string]: any }) {
  return request<API.RVoid>("/system/user/edit/avatar", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 小程序-修改用户 小程序-修改用户 POST /system/user/edit/xcxUser */
export async function editXcxUser(
  body: API.SysUserBo,
  options?: { [key: string]: any }
) {
  return request<API.RSysUserVo>("/system/user/edit/xcxUser", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改用户名称 修改用户名称 POST /system/user/editName */
export async function editName(
  body: API.UserRequest,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user/editName", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出用户列表 导出用户列表 POST /system/user/export */
export async function exportUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.exportUsingPOSTParams,
  options?: { [key: string]: any }
) {
  return request<any>("/system/user/export", {
    method: "POST",
    params: {
      ...params,
      user: undefined,
      ...params["user"],
    },
    ...(options || {}),
  });
}

/** 获取用户信息 获取用户信息 GET /system/user/getInfo */
export async function getInfo1(options?: { [key: string]: any }) {
  return request<API.RUserInfoVo>("/system/user/getInfo", {
    method: "GET",
    ...(options || {}),
  });
}

/** 获取用户列表 获取用户列表 GET /system/user/getUserOption */
export async function getUserOption(options?: { [key: string]: any }) {
  return request<API.RListSysUserOptionVo>("/system/user/getUserOption", {
    method: "GET",
    ...(options || {}),
  });
}

/** 导入数据 导入数据 POST /system/user/importData */
export async function importData(
  body: {
    /** 是否更新已存在数据 */
    updateSupport?: boolean;
  },
  file?: File,
  options?: { [key: string]: any }
) {
  const formData = new FormData();

  if (file) {
    formData.append("file", file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === "object" && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ""));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.RVoid>("/system/user/importData", {
    method: "POST",
    data: formData,
    requestType: "form",
    ...(options || {}),
  });
}

/** 获取导入模板 获取导入模板 POST /system/user/importTemplate */
export async function importTemplate(options?: { [key: string]: any }) {
  return request<any>("/system/user/importTemplate", {
    method: "POST",
    ...(options || {}),
  });
}

/** 获取用户列表 获取用户列表 GET /system/user/list */
export async function list(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listParams,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysUserVo>("/system/user/list", {
    method: "GET",
    params: {
      ...params,
      user: undefined,
      ...params["user"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 重置密码 重置密码 PUT /system/user/resetPwd */
export async function resetPwd(
  body: API.SysUserBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/user/resetPwd", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
