// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 生成验证码 生成验证码 GET /auth/code */
export async function getCode(options?: { [key: string]: any }) {
  return request<API.RCaptchaVo>("/auth/code", {
    method: "GET",
    ...(options || {}),
  });
}

/** 邮箱验证码 邮箱验证码 POST /resource/email/code */
export async function emailCode(
  body: API.EmailRequest,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/resource/email/code", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 短信验证码 短信验证码 GET /resource/sms/code */
export async function smsCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.smsCodeParams,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/resource/sms/code", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
