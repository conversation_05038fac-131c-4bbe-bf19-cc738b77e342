// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 此处后端没有提供注释 POST /luma/generations/ */
export async function generateVideo(
  body: API.GenerateLuma,
  options?: { [key: string]: any }
) {
  return request<string>("/luma/generations/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /luma/generations/${param0} */
export async function getGenerationTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getGenerationTaskParams,
  options?: { [key: string]: any }
) {
  const { taskId: param0, ...queryParams } = params;
  return request<string>(`/luma/generations/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
