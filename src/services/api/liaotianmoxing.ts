// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改聊天模型 修改聊天模型 PUT /system/model */
export async function edit8(
  body: API.ChatModelBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/model", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增聊天模型 新增聊天模型 POST /system/model */
export async function add8(
  body: API.ChatModelBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/model", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取聊天模型详细信息 获取聊天模型详细信息 GET /system/model/${param0} */
export async function getInfo11(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo11Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RChatModelVo>(`/system/model/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除聊天模型 删除聊天模型 DELETE /system/model/${param0} */
export async function remove12(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove12Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/model/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出聊天模型列表 导出聊天模型列表 POST /system/model/export */
export async function export7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export7Params,
  options?: { [key: string]: any }
) {
  return request<any>("/system/model/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询ppt模型信息 查询ppt模型信息 GET /system/model/getPPT */
export async function getPpt(options?: { [key: string]: any }) {
  return request<API.RChatModel>("/system/model/getPPT", {
    method: "GET",
    ...(options || {}),
  });
}

/** 查询聊天模型列表 查询聊天模型列表 GET /system/model/list */
export async function list8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list8Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoChatModelVo>("/system/model/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 查询用户模型列表， 查询用户模型列表， GET /system/model/modelList */
export async function modelList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.modelListParams,
  options?: { [key: string]: any }
) {
  return request<API.RListChatModelVo>("/system/model/modelList", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询根据用户角色查询未隐藏的模型列表 查询根据用户角色查询未隐藏的模型列表 GET /system/model/roleModelList */
export async function roleModelList(options?: { [key: string]: any }) {
  return request<API.RListChatModelVo>("/system/model/roleModelList", {
    method: "GET",
    ...(options || {}),
  });
}
