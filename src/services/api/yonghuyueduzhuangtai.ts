// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改用户阅读状态 修改用户阅读状态 PUT /system/noticeState */
export async function edit7(
  body: API.SysNoticeStateBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/noticeState", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增用户阅读状态 新增用户阅读状态 POST /system/noticeState */
export async function add7(
  body: API.SysNoticeStateBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/system/noticeState", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户阅读状态详细信息 获取用户阅读状态详细信息 GET /system/noticeState/${param0} */
export async function getInfo9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo9Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RSysNoticeStateVo>(`/system/noticeState/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除用户阅读状态 删除用户阅读状态 DELETE /system/noticeState/${param0} */
export async function remove10(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove10Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/system/noticeState/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出用户阅读状态列表 导出用户阅读状态列表 POST /system/noticeState/export */
export async function export6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export6Params,
  options?: { [key: string]: any }
) {
  return request<any>("/system/noticeState/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询用户阅读状态列表 查询用户阅读状态列表 GET /system/noticeState/list */
export async function list6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list6Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysNoticeStateVo>(
    "/system/noticeState/list",
    {
      method: "GET",
      params: {
        ...params,
        bo: undefined,
        ...params["bo"],
        pageQuery: undefined,
        ...params["pageQuery"],
      },
      ...(options || {}),
    }
  );
}
