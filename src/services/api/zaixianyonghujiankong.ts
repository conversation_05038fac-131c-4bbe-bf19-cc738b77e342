// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 强退用户 强退用户 DELETE /monitor/online/${param0} */
export async function forceLogout(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.forceLogoutParams,
  options?: { [key: string]: any }
) {
  const { tokenId: param0, ...queryParams } = params;
  return request<API.RVoid>(`/monitor/online/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取在线用户监控列表 获取在线用户监控列表 GET /monitor/online/list */
export async function list18(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list18Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysUserOnline>("/monitor/online/list", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
