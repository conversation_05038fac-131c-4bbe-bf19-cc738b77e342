// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 此处后端没有提供注释 POST /mj/submit/action */
export async function action(
  body: API.SubmitActionDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/action", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/blend */
export async function blend(
  body: API.SubmitBlendDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/blend", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/change */
export async function change(
  body: API.SubmitChangeDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/change", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/describe */
export async function describe(
  body: API.SubmitDescribeDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/describe", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/imagine */
export async function imagine(
  body: API.SubmitImagineDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/imagine", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/modal */
export async function modal(
  body: API.SubmitModalDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/modal", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/shorten */
export async function shorten(
  body: API.SubmitShortenDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/shorten", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /mj/submit/simple-change */
export async function simpleChange(
  body: API.SubmitSimpleChangeDTO,
  options?: { [key: string]: any }
) {
  return request<string>("/mj/submit/simple-change", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
