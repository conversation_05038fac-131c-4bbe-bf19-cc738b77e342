// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改对象存储配置 修改对象存储配置 PUT /resource/oss/config */
export async function edit15(
  body: API.SysOssConfigBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/resource/oss/config", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增对象存储配置 新增对象存储配置 POST /resource/oss/config */
export async function add15(
  body: API.SysOssConfigBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/resource/oss/config", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取对象存储配置详细信息 获取对象存储配置详细信息 GET /resource/oss/config/${param0} */
export async function getInfo18(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo18Params,
  options?: { [key: string]: any }
) {
  const { ossConfigId: param0, ...queryParams } = params;
  return request<API.RSysOssConfigVo>(`/resource/oss/config/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除对象存储配置 删除对象存储配置 DELETE /resource/oss/config/${param0} */
export async function remove18(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove18Params,
  options?: { [key: string]: any }
) {
  const { ossConfigIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/resource/oss/config/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 状态修改 状态修改 PUT /resource/oss/config/changeStatus */
export async function changeStatus3(
  body: API.SysOssConfigBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/resource/oss/config/changeStatus", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询对象存储配置列表 查询对象存储配置列表 GET /resource/oss/config/list */
export async function list16(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list16Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysOssConfigVo>("/resource/oss/config/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}
