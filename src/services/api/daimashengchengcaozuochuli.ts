// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改保存代码生成业务 修改保存代码生成业务 PUT /tool/gen */
export async function editSave(
  body: API.GenTable,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/tool/gen", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改代码生成业务 修改代码生成业务 GET /tool/gen/${param0} */
export async function getInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfoParams,
  options?: { [key: string]: any }
) {
  const { tableId: param0, ...queryParams } = params;
  return request<API.RMapStringObject>(`/tool/gen/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除代码生成 删除代码生成 DELETE /tool/gen/${param0} */
export async function remove3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove3Params,
  options?: { [key: string]: any }
) {
  const { tableIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/tool/gen/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量生成代码 批量生成代码 GET /tool/gen/batchGenCode */
export async function batchGenCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.batchGenCodeParams,
  options?: { [key: string]: any }
) {
  return request<any>("/tool/gen/batchGenCode", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询数据表字段列表 查询数据表字段列表 GET /tool/gen/column/${param0} */
export async function columnList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.columnListParams,
  options?: { [key: string]: any }
) {
  const { tableId: param0, ...queryParams } = params;
  return request<API.TableDataInfoGenTableColumn>(
    `/tool/gen/column/${param0}`,
    {
      method: "GET",
      params: {
        ...queryParams,
      },
      ...(options || {}),
    }
  );
}

/** 查询数据库列表 查询数据库列表 GET /tool/gen/db/list */
export async function dataList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dataListParams,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoGenTable>("/tool/gen/db/list", {
    method: "GET",
    params: {
      ...params,
      genTable: undefined,
      ...params["genTable"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 生成代码（下载方式） 生成代码（下载方式） GET /tool/gen/download/${param0} */
export async function download(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.downloadParams,
  options?: { [key: string]: any }
) {
  const { tableId: param0, ...queryParams } = params;
  return request<any>(`/tool/gen/download/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 生成代码（自定义路径） 生成代码（自定义路径） GET /tool/gen/genCode/${param0} */
export async function genCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.genCodeParams,
  options?: { [key: string]: any }
) {
  const { tableName: param0, ...queryParams } = params;
  return request<API.RVoid>(`/tool/gen/genCode/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询数据源名称列表 查询数据源名称列表 GET /tool/gen/getDataNames */
export async function getCurrentDataSourceNameList(options?: {
  [key: string]: any;
}) {
  return request<API.RObject>("/tool/gen/getDataNames", {
    method: "GET",
    ...(options || {}),
  });
}

/** 导入表结构（保存） 导入表结构（保存） POST /tool/gen/importTable */
export async function importTableSave(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.importTableSaveParams,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/tool/gen/importTable", {
    method: "POST",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询代码生成列表 查询代码生成列表 GET /tool/gen/list */
export async function genList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.genListParams,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoGenTable>("/tool/gen/list", {
    method: "GET",
    params: {
      ...params,
      genTable: undefined,
      ...params["genTable"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 预览代码 预览代码 GET /tool/gen/preview/${param0} */
export async function preview(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.previewParams,
  options?: { [key: string]: any }
) {
  const { tableId: param0, ...queryParams } = params;
  return request<API.RMapStringString>(`/tool/gen/preview/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 同步数据库 同步数据库 GET /tool/gen/synchDb/${param0} */
export async function synchDb(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.synchDbParams,
  options?: { [key: string]: any }
) {
  const { tableName: param0, ...queryParams } = params;
  return request<API.RVoid>(`/tool/gen/synchDb/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
