// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 语音转文本 语音转文本 POST /chat/audio */
export async function audio(body: {}, options?: { [key: string]: any }) {
  return request<API.WhisperResponse>("/chat/audio", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 聊天接口 聊天接口 POST /chat/send */
export async function sseChat(
  body: API.ChatRequest,
  options?: { [key: string]: any }
) {
  return request<API.SseEmitter>("/chat/send", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 文本转语音 文本转语音 POST /chat/speech */
export async function speech(
  body: API.TextToSpeech,
  options?: { [key: string]: any }
) {
  return request<string>("/chat/speech", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件 上传文件 POST /chat/upload */
export async function upload2(body: {}, options?: { [key: string]: any }) {
  return request<API.UploadFileResponse>("/chat/upload", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
