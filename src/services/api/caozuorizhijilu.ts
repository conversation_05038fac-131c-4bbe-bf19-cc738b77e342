// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 批量删除操作日志记录 批量删除操作日志记录 DELETE /monitor/operlog/${param0} */
export async function remove21(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove21Params,
  options?: { [key: string]: any }
) {
  const { operIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/monitor/operlog/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 清理操作日志记录 清理操作日志记录 DELETE /monitor/operlog/clean */
export async function clean(options?: { [key: string]: any }) {
  return request<API.RVoid>("/monitor/operlog/clean", {
    method: "DELETE",
    ...(options || {}),
  });
}

/** 导出操作日志记录列表 导出操作日志记录列表 POST /monitor/operlog/export */
export async function export12(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export12Params,
  options?: { [key: string]: any }
) {
  return request<any>("/monitor/operlog/export", {
    method: "POST",
    params: {
      ...params,
      operLog: undefined,
      ...params["operLog"],
    },
    ...(options || {}),
  });
}

/** 获取操作日志记录列表 获取操作日志记录列表 GET /monitor/operlog/list */
export async function list17(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list17Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysOperLogVo>("/monitor/operlog/list", {
    method: "GET",
    params: {
      ...params,
      operLog: undefined,
      ...params["operLog"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}
