// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改配置信息 修改配置信息 PUT /chat/config */
export async function edit18(
  body: API.ChatConfigBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/chat/config", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取配置信息详细信息 获取配置信息详细信息 GET /chat/config/${param0} */
export async function getInfo20(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInfo20Params,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RChatConfigVo>(`/chat/config/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除配置信息 删除配置信息 DELETE /chat/config/${param0} */
export async function remove23(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove23Params,
  options?: { [key: string]: any }
) {
  const { ids: param0, ...queryParams } = params;
  return request<API.RVoid>(`/chat/config/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据参数键名查询系统参数值 根据参数键名查询系统参数值 GET /chat/config/configKey/${param0} */
export async function getConfigKey1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getConfigKey1Params,
  options?: { [key: string]: any }
) {
  const { configKey: param0, ...queryParams } = params;
  return request<API.RString>(`/chat/config/configKey/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出配置信息列表 导出配置信息列表 POST /chat/config/export */
export async function export15(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export15Params,
  options?: { [key: string]: any }
) {
  return request<any>("/chat/config/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询配置信息列表 查询配置信息列表 GET /chat/config/list */
export async function list21(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list21Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoChatConfigVo>("/chat/config/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 新增配置信息 新增配置信息 POST /chat/config/saveOrUpdate */
export async function saveOrUpdate(
  body: API.ChatConfigBo[],
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/chat/config/saveOrUpdate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询系统参数 查询系统参数 GET /chat/config/sysConfigKey */
export async function getSysConfigKey(options?: { [key: string]: any }) {
  return request<API.RListChatConfigVo>("/chat/config/sysConfigKey", {
    method: "GET",
    ...(options || {}),
  });
}
