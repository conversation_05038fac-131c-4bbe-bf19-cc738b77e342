// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 此处后端没有提供注释 GET /sunoapi/feed/${param0} */
export async function feed(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.feedParams,
  options?: { [key: string]: any }
) {
  const { taskId: param0, ...queryParams } = params;
  return request<string>(`/sunoapi/feed/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /sunoapi/generate */
export async function generate(
  body: API.GenerateSuno,
  options?: { [key: string]: any }
) {
  return request<string>("/sunoapi/generate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /sunoapi/generate/lyrics/ */
export async function generate1(
  body: API.GenerateLyric,
  options?: { [key: string]: any }
) {
  return request<string>("/sunoapi/generate/lyrics/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /sunoapi/lyrics/${param0} */
export async function lyrics(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.lyricsParams,
  options?: { [key: string]: any }
) {
  const { taskId: param0, ...queryParams } = params;
  return request<string>(`/sunoapi/lyrics/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
