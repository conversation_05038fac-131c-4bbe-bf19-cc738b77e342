// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 批量删除登录日志 批量删除登录日志 DELETE /monitor/logininfor/${param0} */
export async function remove22(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove22Params,
  options?: { [key: string]: any }
) {
  const { infoIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/monitor/logininfor/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 清理系统访问记录 清理系统访问记录 DELETE /monitor/logininfor/clean */
export async function clean1(options?: { [key: string]: any }) {
  return request<API.RVoid>("/monitor/logininfor/clean", {
    method: "DELETE",
    ...(options || {}),
  });
}

/** 导出系统访问记录列表 导出系统访问记录列表 POST /monitor/logininfor/export */
export async function export13(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export13Params,
  options?: { [key: string]: any }
) {
  return request<any>("/monitor/logininfor/export", {
    method: "POST",
    params: {
      ...params,
      logininfor: undefined,
      ...params["logininfor"],
    },
    ...(options || {}),
  });
}

/** 获取系统访问记录列表 获取系统访问记录列表 GET /monitor/logininfor/list */
export async function list19(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list19Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoSysLogininforVo>("/monitor/logininfor/list", {
    method: "GET",
    params: {
      ...params,
      logininfor: undefined,
      ...params["logininfor"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /monitor/logininfor/unlock/${param0} */
export async function unlock(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.unlockParams,
  options?: { [key: string]: any }
) {
  const { userName: param0, ...queryParams } = params;
  return request<API.RVoid>(`/monitor/logininfor/unlock/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
