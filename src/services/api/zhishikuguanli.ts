// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 获取知识库附件详细信息 获取知识库附件详细信息 GET /knowledge/attach/info/${param0} */
export async function getAttachInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAttachInfoParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RKnowledgeAttachVo>(`/knowledge/attach/info/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除知识库附件 删除知识库附件 POST /knowledge/attach/remove/${param0} */
export async function removeAttach(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.removeAttachParams,
  options?: { [key: string]: any }
) {
  const { kid: param0, ...queryParams } = params;
  return request<API.RVoid>(`/knowledge/attach/remove/${param0}`, {
    method: "POST",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 上传知识库附件 上传知识库附件 POST /knowledge/attach/upload */
export async function upload1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.upload1Params,
  options?: { [key: string]: any }
) {
  return request<API.RString>("/knowledge/attach/upload", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询知识附件信息 查询知识附件信息 GET /knowledge/detail/${param0} */
export async function attach(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.attachParams,
  options?: { [key: string]: any }
) {
  const { kid: param0, ...queryParams } = params;
  return request<API.TableDataInfoKnowledgeAttachVo>(
    `/knowledge/detail/${param0}`,
    {
      method: "GET",
      params: {
        ...queryParams,
        bo: undefined,
        ...queryParams["bo"],
        pageQuery: undefined,
        ...queryParams["pageQuery"],
      },
      ...(options || {}),
    }
  );
}

/** 修改知识库 修改知识库 POST /knowledge/edit */
export async function edit19(
  body: API.KnowledgeInfoBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/knowledge/edit", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出知识库列表 导出知识库列表 POST /knowledge/export */
export async function export14(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.export14Params,
  options?: { [key: string]: any }
) {
  return request<any>("/knowledge/export", {
    method: "POST",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
    },
    ...(options || {}),
  });
}

/** 查询知识片段 查询知识片段 GET /knowledge/fragment/list/${param0} */
export async function fragmentList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fragmentListParams,
  options?: { [key: string]: any }
) {
  const { docId: param0, ...queryParams } = params;
  return request<API.TableDataInfoKnowledgeFragmentVo>(
    `/knowledge/fragment/list/${param0}`,
    {
      method: "GET",
      params: {
        ...queryParams,
        bo: undefined,
        ...queryParams["bo"],
        pageQuery: undefined,
        ...queryParams["pageQuery"],
      },
      ...(options || {}),
    }
  );
}

/** 根据用户信息查询本地知识库 根据用户信息查询本地知识库 GET /knowledge/list */
export async function list20(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.list20Params,
  options?: { [key: string]: any }
) {
  return request<API.TableDataInfoKnowledgeInfoVo>("/knowledge/list", {
    method: "GET",
    params: {
      ...params,
      bo: undefined,
      ...params["bo"],
      pageQuery: undefined,
      ...params["pageQuery"],
    },
    ...(options || {}),
  });
}

/** 删除知识库 删除知识库 POST /knowledge/remove/${param0} */
export async function remove(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.removeParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RString>(`/knowledge/remove/${param0}`, {
    method: "POST",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新增知识库 新增知识库 POST /knowledge/save */
export async function save(
  body: API.KnowledgeInfoBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/knowledge/save", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件翻译 上传文件翻译 POST /knowledge/translationByFile */
export async function translationByFile(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.translationByFileParams,
  body: {},
  options?: { [key: string]: any }
) {
  return request<string>("/knowledge/translationByFile", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
