<template>
	<div class="flex h-full bg-white dark:bg-[#101014]">
		<!-- 左侧栏 -->
		<div :class="[
			'border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300 bg-white dark:bg-[#101014]',
			isCollapsed ? 'w-0 overflow-hidden' : 'w-[20%] min-w-[280px]'
		]">
			<!-- 新建任务按钮 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<button
					class="w-full px-4 py-3 rounded-lg text-white font-medium transition-all duration-300 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 gradient-button"
					@click="handleCreateTask">
					<div class="flex items-center justify-center space-x-2">
						<NIcon size="18">
							<AddOutline />
						</NIcon>
						<span>新建任务</span>
					</div>
				</button>
			</div>

			<!-- 搜索框 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<NInput v-model:value="searchValue" placeholder="搜索任务列表" clearable>
					<template #prefix>
						<NIcon>
							<SearchOutline />
						</NIcon>
					</template>
				</NInput>
			</div>

			<!-- 任务列表 -->
			<div class="flex-1 overflow-y-auto">
				<div v-for="task in filteredTasks" :key="task.id"
					class="p-4 border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer task-item"
					:class="{ 'bg-blue-50 dark:bg-blue-900/20': selectedTask?.id === task.id }" @click="selectTask(task)">
					<div class="flex items-start space-x-3">
						<!-- 文件类型图标 -->
						<div class="flex-shrink-0 mt-1">
							<NIcon size="20" :color="getFileTypeColor(task.fileType)">
								<DocumentTextOutline v-if="task.fileType === 'doc'" />
								<DocumentOutline v-else-if="task.fileType === 'pdf'" />
								<DocumentTextOutline v-else />
							</NIcon>
						</div>
						<!-- 任务信息 -->
						<div class="flex-1 min-w-0">
							<div class="font-medium text-gray-900 dark:text-gray-100 truncate">
								{{ task.fileName }}
							</div>
							<!-- 状态和日期在同一行 -->
							<div class="flex items-center justify-between mt-2">
								<div class="flex items-center space-x-2">
									<!-- 运行中状态的loading动画 -->
									<div v-if="task.status === 'processing'"
										class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
									<NBadge :value="getStatusText(task.status)" :type="getStatusType(task.status)" />
								</div>
								<div class="text-xs text-gray-500 dark:text-gray-400">
									{{ formatDate(task.createDate) }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 收起/展开按钮 -->
		<div class="flex flex-col justify-center">
			<button
				class="w-6 h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 border-l border-r border-gray-200 dark:border-gray-700 flex items-center justify-center transition-colors"
				@click="toggleSidebar">
				<NIcon size="14">
					<ChevronBackOutline v-if="!isCollapsed" />
					<ChevronForwardOutline v-else />
				</NIcon>
			</button>
		</div>

		<!-- 主界面区域 -->
		<div class="flex-1 flex h-full">
			<!-- 显示新建任务界面或处理中界面 -->
			<div v-if="!isProcessing && !selectedTask" class="flex-1 flex flex-col h-full justify-center">
				<div class="max-w-7xl mx-auto w-full">
					<!-- 主标题区域 -->
					<div class="p-8 text-center">
						<h1
							class="mb-10 text-5xl font-bold mb-4 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600 bg-clip-text text-transparent">
							智能诊断 一览无余
						</h1>
						<p class="text-xl text-gray-600 dark:text-gray-400 font-medium">
							精准定位条款原文，提供专业的风险判断，风险分析及修改示例
						</p>
					</div>

					<!-- 选择器区域 -->
					<div class="px-8 mb-6">
						<div class="flex justify-center">
							<div class="flex space-x-8 items-center">
								<!-- 合同场景选择 -->
								<div class="flex items-center space-x-3">
									<label class="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
										合同场景
									</label>
									<NSelect v-model:value="contractScenario" :options="contractScenarioOptions" placeholder="请选择合同场景"
										style="width: 200px;" />
								</div>
								<!-- 审查立场选择 -->
								<div class="flex items-center space-x-3">
									<label class="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
										审查立场
									</label>
									<NSelect v-model:value="reviewStance" :options="reviewStanceOptions" placeholder="请选择审查立场"
										style="width: 200px;" />
								</div>
							</div>
						</div>
					</div>

					<!-- 上传文件区域 -->
					<div class="px-8 mb-8">
						<div
							class="upload-area-elegant relative overflow-hidden border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-xl p-12 text-center cursor-pointer"
							@click="handleUploadClick" @dragover.prevent @drop.prevent="handleFileDrop">
							<!-- 优雅的背景层 -->
							<div class="upload-bg-elegant absolute inset-0"></div>
							<div class="upload-pattern absolute inset-0"></div>

							<!-- 装饰性几何图形 -->
							<div class="upload-decorations absolute inset-0 pointer-events-none">
								<div class="decoration-dot decoration-dot-1"></div>
								<div class="decoration-dot decoration-dot-2"></div>
								<div class="decoration-dot decoration-dot-3"></div>
								<div class="decoration-line decoration-line-1"></div>
								<div class="decoration-line decoration-line-2"></div>
							</div>

							<!-- 内容区域 -->
							<div class="relative z-10 flex flex-col items-center">
								<div class="upload-icon-container mb-6">
									<NIcon size="56" class="upload-icon text-blue-500">
										<CloudUploadOutline />
									</NIcon>
								</div>
								<div class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3 upload-text">
									将文件拖拽到此处或<span class="text-blue-500 cursor-pointer hover:text-blue-600 transition-colors">点击上传</span>
								</div>
								<div class="text-sm text-gray-500 dark:text-gray-400 upload-description">
									文档格式：DOC/DOCX/PDF，单次最多上传10份，单份大小10M内
								</div>
							</div>
						</div>

						<!-- 已上传文件列表 -->
						<div v-if="uploadedFiles.length > 0" class="mt-4">
							<div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
								已上传文件 ({{ uploadedFiles.length }}/10)
							</div>
							<div class="space-y-2">
								<div v-for="(file, index) in uploadedFiles" :key="index"
									class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
									<div class="flex items-center space-x-3">
										<NIcon size="20" :color="getFileTypeColor(getFileExtension(file.name))">
											<DocumentTextOutline v-if="getFileExtension(file.name) === 'doc'" />
											<DocumentOutline v-else />
										</NIcon>
										<div>
											<div class="text-sm font-medium text-gray-900 dark:text-gray-100">
												{{ file.name }}
											</div>
											<div class="text-xs text-gray-500 dark:text-gray-400">
												{{ formatFileSize(file.size) }}
											</div>
										</div>
									</div>
									<NButton size="small" type="error" ghost @click="removeFile(index)">
										删除
									</NButton>
								</div>
							</div>
						</div>

						<!-- 隐藏的文件输入 -->
						<input ref="fileInput" type="file" multiple accept=".doc,.docx,.pdf" class="hidden"
							@change="handleFileSelect" />
					</div>

					<!-- 按钮区域 -->
					<div class="px-8 pb-8">
						<div class="flex space-x-4 justify-center">
							<button
								class="px-8 py-3 h-12 rounded-lg text-white font-medium transition-all duration-300 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 gradient-button flex items-center justify-center min-w-[140px]"
								@click="handleStartReview">
								开始合同审查
							</button>
							<button
								class="px-8 py-3 h-12 rounded-lg font-medium transition-all duration-300 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-center min-w-[140px]"
								@click="handleManageChecklist">
								审核清单管理
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- 处理中界面：左中右三栏结构 -->
			<div v-else-if="isProcessing || (selectedTask && selectedTask.status === 'processing')"
				class="flex-1 flex h-full">
				<!-- 中间部分：文件解析 -->
				<div
					class="flex-1 flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
					<div class="text-center">
						<!-- 自定义loading动画 -->
						<div class="relative mb-8">
							<div class="loader loader-medium mx-auto"></div>
						</div>
						<!-- 文字 -->
						<div class="text-xl font-semibold text-blue-600 dark:text-blue-400 mb-2">
							文件解析中......
						</div>
					</div>
				</div>

				<!-- 右边部分：审查进度 -->
				<div
					class="w-1/3 flex flex-col items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50 dark:from-gray-800 dark:to-gray-900 border-l border-gray-200 dark:border-gray-700">
					<div class="text-center px-8">
						<!-- 自定义loading动画 -->
						<div class="relative mb-8">
							<div class="loader loader-large mx-auto"></div>
						</div>
						<!-- 文字 -->
						<div class="text-lg font-semibold text-purple-600 dark:text-purple-400 mb-2">
							审查中......{{ Math.round(progress) }}%
						</div>
						<div class="text-sm text-gray-500 dark:text-gray-400">
							任务预计处理3-5分钟，期间您可自由使用其他功能
						</div>
					</div>
				</div>
			</div>

			<!-- 审查完成界面：中间合同内容 + 右侧审查结果 -->
			<div v-else-if="selectedTask && selectedTask.status === 'completed'" class="flex-1 flex h-full">
				<!-- 中间部分：合同内容显示 -->
				<div class="flex-1 flex flex-col bg-white dark:bg-gray-900">
					<!-- 合同内容区域 -->
					<div class="flex-1 overflow-y-auto p-6">
						<div class="contract-content" v-html="contractHtmlContent" ref="contractContentRef"></div>
					</div>
				</div>

				<!-- 右侧部分：审查结果 -->
				<div
					class="w-1/3 min-w-[400px] border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex flex-col">
					<!-- 审查结果标题栏 -->
					<div class="p-4 border-b border-gray-200 dark:border-gray-700">
						<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">审查结果</h3>
					</div>

					<!-- 标签页 -->
					<div class="border-b border-gray-200 dark:border-gray-700">
						<div class="flex">
							<button v-for="tab in resultTabs" :key="tab.key" @click="activeTab = tab.key" :class="[
								'flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
								activeTab === tab.key
									? 'border-blue-500 text-blue-600 dark:text-blue-400'
									: 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
							]">
								<div class="flex items-center justify-center space-x-1">
									<div v-if="tab.key !== 'all'" :class="[
										'w-2 h-2 rounded-full',
										tab.key === 'major' ? 'bg-red-500' : 'bg-yellow-500'
									]"></div>
									<span>{{ tab.label }}</span>
									<span class="text-xs">({{ getTabCount(tab.key) }})</span>
								</div>
							</button>
						</div>
					</div>

					<!-- 审查结果列表 -->
					<div class="flex-1 overflow-y-auto">
						<div class="p-4 space-y-3">
							<div v-for="(result, index) in filteredResults" :key="result.id"
								class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
								<!-- 结果项标题 -->
								<div @click="toggleResultExpand(result.id)"
									class="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
									<div class="flex items-center space-x-3 flex-1">
										<div :class="[
											'w-2 h-2 rounded-full flex-shrink-0',
											result.riskLevel === 'major' ? 'bg-red-500' : 'bg-yellow-500'
										]"></div>
										<span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
											{{ result.title }}
										</span>
									</div>
									<NIcon size="16" :class="[
										'text-gray-400 transition-transform',
										expandedResults.includes(result.id) ? 'rotate-180' : ''
									]">
										<ChevronDownOutline />
									</NIcon>
								</div>

								<!-- 展开的详情内容 -->
								<div v-if="expandedResults.includes(result.id)"
									class="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800">
									<!-- 条款定位 -->
									<div class="flex items-center justify-between mb-4">
										<span class="text-sm font-medium text-gray-700 dark:text-gray-300">条款定位</span>
										<div class="flex items-center space-x-2">
											<button @click="navigateClause(result.id, 'prev')"
												:disabled="getCurrentClauseIndex(result.id) <= 0"
												class="w-6 h-6 flex items-center justify-center rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
												<NIcon size="12">
													<ChevronBackOutline />
												</NIcon>
											</button>
											<span class="text-xs text-gray-500 dark:text-gray-400 min-w-[40px] text-center">
												{{ getCurrentClauseIndex(result.id) + 1 }}/{{ result.clauses.length }}
											</span>
											<button @click="navigateClause(result.id, 'next')"
												:disabled="getCurrentClauseIndex(result.id) >= result.clauses.length - 1"
												class="w-6 h-6 flex items-center justify-center rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
												<NIcon size="12">
													<ChevronForwardOutline />
												</NIcon>
											</button>
										</div>
									</div>

									<!-- 当前风险点标签 -->
									<div class="mb-4">
										<span :class="[
											'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
											result.riskLevel === 'major'
												? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
												: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
										]">
											风险点{{ getCurrentClauseIndex(result.id) + 1 }}
										</span>
										<span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
											{{ result.clauses[getCurrentClauseIndex(result.id)]?.title }}
										</span>
									</div>

									<!-- 详情字段 -->
									<div class="space-y-3">
										<div>
											<div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">风险说明</div>
											<div class="text-sm text-gray-800 dark:text-gray-200">{{ result.riskDescription }}</div>
										</div>
										<div>
											<div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">风险分析</div>
											<div class="text-sm text-gray-800 dark:text-gray-200">{{ result.riskAnalysis }}</div>
										</div>
										<div>
											<div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">修改示例</div>
											<div class="text-sm text-gray-800 dark:text-gray-200">{{ result.modificationExample }}</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 导出结果按钮 -->
					<div class="p-4 border-t border-gray-200 dark:border-gray-700">
						<div class="flex justify-end">
							<NButton type="primary" @click="handleExportResults">
								导出结果
							</NButton>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	AddOutline,
	SearchOutline,
	DocumentTextOutline,
	DocumentOutline,
	ChevronBackOutline,
	ChevronForwardOutline,
	CloudUploadOutline,
	ChevronDownOutline,
} from "@vicons/ionicons5";
import {
	NIcon,
	NInput,
	NSelect,
	NButton,
	NBadge,
	useMessage,
} from "naive-ui";
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

const message = useMessage();
const router = useRouter();
const route = useRoute();

// 左侧栏状态
const isCollapsed = ref(false);
const searchValue = ref("");

// 选择器数据
const contractScenario = ref("");
const reviewStance = ref("");

// 文件上传
const fileInput = ref<HTMLInputElement>();
const uploadedFiles = ref<File[]>([]);

// 任务列表
const selectedTask = ref<any>(null);

// 处理状态
const isProcessing = ref(false);
const progress = ref(0);

// 审查结果相关
const contractContentRef = ref<HTMLElement>();
const contractHtmlContent = ref('');
const activeTab = ref('all');
const expandedResults = ref<number[]>([]);
const currentClauseIndexes = ref<Record<number, number>>({});

// 标签页配置
const resultTabs = [
	{ key: 'all', label: '全部' },
	{ key: 'major', label: '重大风险' },
	{ key: 'general', label: '一般风险' }
];

// 模拟审查结果数据
const reviewResults = ref([
	{
		id: 1,
		title: '主体信息条款',
		riskLevel: 'major',
		riskDescription: '合同主体身份信息不完整，甲方信息相对完整，但乙方信息存在大量空白，缺少具体的身份证号、联系地址等关键信息。',
		riskAnalysis: '主体信息不完整会影响合同的有效性和可执行性，在发生纠纷时难以确定责任主体，增加法律风险。特别是乙方信息的缺失可能导致甲方在维权时面临困难。',
		modificationExample: '建议在乙方信息中明确填写：姓名/名称、身份证号/营业执照号、详细联系地址、联系电话等完整信息，确保信息真实有效。',
		clauses: [
			{ id: 'clause-1-1', title: '甲方信息条款' },
			{ id: 'clause-1-2', title: '乙方信息条款' }
		]
	},
	{
		id: 2,
		title: '租赁期限条款',
		riskLevel: 'general',
		riskDescription: '租赁期限约定基本明确，但在续租条件和优先权方面可以进一步完善。',
		riskAnalysis: '当前期限约定较为清晰，但续租条款可能在实际执行中产生争议，建议增加更详细的续租条件和程序。',
		modificationExample: '建议在续租条款中明确续租的具体条件、租金调整机制、续租期限等详细内容。',
		clauses: [
			{ id: 'clause-2-1', title: '租赁期限及续租条款' }
		]
	},
	{
		id: 3,
		title: '租金支付条款',
		riskLevel: 'major',
		riskDescription: '租金支付条款存在多处风险点，包括支付时间、逾期责任、押金管理等方面都需要完善。',
		riskAnalysis: '支付条款的不完善容易引发支付纠纷，特别是逾期责任和押金退还条件需要更加明确，以保护双方合法权益。',
		modificationExample: '建议明确支付日期、逾期违约金计算方式、押金退还条件和程序，确保支付条款的可操作性。',
		clauses: [
			{ id: 'clause-3-1', title: '租金标准及支付方式' },
			{ id: 'clause-3-2', title: '押金条款' },
			{ id: 'clause-3-3', title: '逾期付款责任' }
		]
	},
	{
		id: 4,
		title: '违约责任条款',
		riskLevel: 'general',
		riskDescription: '违约责任条款基本完整，但在具体违约情形的认定和责任承担方式上可以更加细化。',
		riskAnalysis: '违约责任条款对于合同的约束力至关重要，当前条款基本覆盖了主要违约情形，但在执行标准上还可以进一步明确。',
		modificationExample: '建议对违约情形进行更详细的分类，明确不同违约行为对应的具体责任和救济措施。',
		clauses: [
			{ id: 'clause-4-1', title: '违约责任及救济措施' }
		]
	}
]);

// 模拟任务数据
const tasks = ref([
	{
		id: 1,
		fileName: "租赁合同_2024.docx",
		fileType: "doc",
		createDate: new Date("2024-01-15"),
		status: "completed"
	},
	{
		id: 2,
		fileName: "销售协议_V2.pdf",
		fileType: "pdf",
		createDate: new Date("2024-01-14"),
		status: "processing"
	},
	{
		id: 3,
		fileName: "服务合同_草稿.doc",
		fileType: "doc",
		createDate: new Date("2024-01-13"),
		status: "pending"
	}
]);

// 合同场景选项
const contractScenarioOptions = [
	{ label: "租赁合同", value: "lease" },
	{ label: "销售合同", value: "sales" },
	{ label: "服务合同", value: "service" },
	{ label: "采购合同", value: "procurement" },
	{ label: "劳动合同", value: "labor" },
];

// 审查立场选项
const reviewStanceOptions = [
	{ label: "甲方立场", value: "party_a" },
	{ label: "乙方立场", value: "party_b" },
	{ label: "中立立场", value: "neutral" },
];

// 过滤后的任务列表
const filteredTasks = computed(() => {
	if (!searchValue.value) return tasks.value;
	return tasks.value.filter(task =>
		task.fileName.toLowerCase().includes(searchValue.value.toLowerCase())
	);
});

// 过滤后的审查结果
const filteredResults = computed(() => {
	if (activeTab.value === 'all') {
		return reviewResults.value;
	} else if (activeTab.value === 'major') {
		return reviewResults.value.filter(result => result.riskLevel === 'major');
	} else if (activeTab.value === 'general') {
		return reviewResults.value.filter(result => result.riskLevel === 'general');
	}
	return [];
});

// 获取文件类型颜色
const getFileTypeColor = (fileType: string) => {
	switch (fileType) {
		case "doc":
			return "#1890ff";
		case "pdf":
			return "#f5222d";
		default:
			return "#666666";
	}
};

// 获取状态文本
const getStatusText = (status: string) => {
	switch (status) {
		case "completed":
			return "已完成";
		case "processing":
			return "处理中";
		case "pending":
			return "待处理";
		default:
			return "未知";
	}
};

// 获取状态类型
const getStatusType = (status: string) => {
	switch (status) {
		case "completed":
			return "success";
		case "processing":
			return "warning";
		case "pending":
			return "info";
		default:
			return "default";
	}
};

// 格式化日期
const formatDate = (date: Date) => {
	return date.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	});
};

// 切换侧边栏
const toggleSidebar = () => {
	isCollapsed.value = !isCollapsed.value;
};

// 选择任务
const selectTask = (task: any) => {
	selectedTask.value = task;

	// 如果选择的是处理中的任务，显示处理界面
	if (task.status === 'processing') {
		isProcessing.value = false; // 不显示新建任务的处理界面，而是显示已有任务的处理界面
		// 如果需要显示实时进度，可以在这里添加逻辑
		// 这里可以从服务器获取实际进度，暂时设置一个模拟值
		progress.value = Math.floor(Math.random() * 80) + 10; // 10-90之间的随机进度
	} else if (task.status === 'completed') {
		// 如果选择的是已完成的任务，设置合同内容和审查结果
		setMockContractContent();
		// 重置审查结果状态
		activeTab.value = 'all';
		expandedResults.value = [];
		currentClauseIndexes.value = {};
		// 移除所有高亮
		setTimeout(() => {
			if (contractContentRef.value) {
				const highlights = contractContentRef.value.querySelectorAll('.risk-highlight');
				highlights.forEach(el => {
					el.classList.remove('risk-highlight', 'risk-major', 'risk-general');
				});
			}
		}, 100);
	}
};

// 新建任务
const handleCreateTask = () => {
	// 清空选择器和上传文件
	contractScenario.value = "";
	reviewStance.value = "";
	uploadedFiles.value = [];
	selectedTask.value = null;
	message.info("已清空当前数据，可以开始新建任务");
};

// 处理文件上传点击
const handleUploadClick = () => {
	fileInput.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
	const target = event.target as HTMLInputElement;
	if (target.files) {
		handleFiles(Array.from(target.files));
	}
};

// 处理文件拖拽
const handleFileDrop = (event: DragEvent) => {
	if (event.dataTransfer?.files) {
		handleFiles(Array.from(event.dataTransfer.files));
	}
};

// 处理文件
const handleFiles = (files: File[]) => {
	// 验证文件类型和大小
	const validFiles = files.filter(file => {
		const validTypes = ['.doc', '.docx', '.pdf'];
		const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
		const isValidType = validTypes.includes(fileExtension);
		const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

		if (!isValidType) {
			message.error(`文件 ${file.name} 格式不支持`);
			return false;
		}
		if (!isValidSize) {
			message.error(`文件 ${file.name} 大小超过10MB`);
			return false;
		}
		return true;
	});

	if (uploadedFiles.value.length + validFiles.length > 10) {
		message.error("最多只能上传10个文件");
		return;
	}

	uploadedFiles.value.push(...validFiles);
	message.success(`成功添加 ${validFiles.length} 个文件`);
};

// 获取文件扩展名
const getFileExtension = (fileName: string) => {
	const extension = fileName.split('.').pop()?.toLowerCase();
	if (extension === 'docx' || extension === 'doc') {
		return 'doc';
	}
	return extension || '';
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
	if (bytes === 0) return '0 Bytes';
	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 删除文件
const removeFile = (index: number) => {
	uploadedFiles.value.splice(index, 1);
	message.success("文件删除成功");
};

// 开始合同审查
const handleStartReview = () => {
	if (!contractScenario.value) {
		message.error("请选择合同场景");
		return;
	}
	if (!reviewStance.value) {
		message.error("请选择审查立场");
		return;
	}
	if (uploadedFiles.value.length === 0) {
		message.error("请上传合同文件");
		return;
	}

	// 创建新任务并添加到任务列表
	const newTask = {
		id: Date.now(),
		fileName: uploadedFiles.value[0].name, // 使用第一个文件名
		fileType: getFileExtension(uploadedFiles.value[0].name),
		createDate: new Date(),
		status: "processing"
	};

	tasks.value.unshift(newTask); // 添加到列表顶部
	selectedTask.value = newTask;
	isProcessing.value = true;
	progress.value = 0;

	message.success("开始合同审查...");

	// 模拟进度更新
	startProgressSimulation();
};

// 模拟进度更新
const startProgressSimulation = () => {
	const interval = setInterval(() => {
		if (progress.value < 100) {
			// 随机增加进度，但确保不会超过100
			const increment = Math.random() * 15 + 5; // 5-20之间的随机增量
			progress.value = Math.min(progress.value + increment, 100);
		} else {
			clearInterval(interval);
			// 完成后可以跳转到结果页面或显示完成状态
			setTimeout(() => {
				if (selectedTask.value) {
					selectedTask.value.status = "completed";
				}
				isProcessing.value = false;
				// 设置模拟的合同HTML内容
				setMockContractContent();
				message.success("合同审查完成！");
			}, 1000);
		}
	}, 800); // 每800ms更新一次进度
};

// 审核清单管理
const handleManageChecklist = () => {
	router.push("/contract/index");
};

// 获取标签页数量
const getTabCount = (tabKey: string) => {
	if (tabKey === 'all') {
		return reviewResults.value.length;
	} else if (tabKey === 'major') {
		return reviewResults.value.filter(result => result.riskLevel === 'major').length;
	} else if (tabKey === 'general') {
		return reviewResults.value.filter(result => result.riskLevel === 'general').length;
	}
	return 0;
};

// 切换结果展开状态（手风琴效果，一次只能展开一个）
const toggleResultExpand = (resultId: number) => {
	const isCurrentlyExpanded = expandedResults.value.includes(resultId);

	if (isCurrentlyExpanded) {
		// 如果当前项已展开，则收起
		expandedResults.value = [];
	} else {
		// 收起所有其他项，只展开当前项
		expandedResults.value = [resultId];
		// 初始化条款索引
		if (!(resultId in currentClauseIndexes.value)) {
			currentClauseIndexes.value[resultId] = 0;
		}
		// 立即高亮第一个条款
		const result = reviewResults.value.find(r => r.id === resultId);
		if (result && result.clauses.length > 0) {
			highlightClause(result.clauses[0], result.riskLevel);
		}
	}
};

// 获取当前条款索引
const getCurrentClauseIndex = (resultId: number) => {
	return currentClauseIndexes.value[resultId] || 0;
};

// 导航到条款
const navigateClause = (resultId: number, direction: 'prev' | 'next') => {
	const result = reviewResults.value.find(r => r.id === resultId);
	if (!result) return;

	const currentIndex = getCurrentClauseIndex(resultId);
	let newIndex = currentIndex;

	if (direction === 'prev' && currentIndex > 0) {
		newIndex = currentIndex - 1;
	} else if (direction === 'next' && currentIndex < result.clauses.length - 1) {
		newIndex = currentIndex + 1;
	}

	currentClauseIndexes.value[resultId] = newIndex;

	// 滚动到对应的条款位置并高亮
	const clause = result.clauses[newIndex];
	highlightClause(clause, result.riskLevel);
};

// 高亮条款
const highlightClause = (clause: any, riskLevel: string) => {
	if (!contractContentRef.value) return;

	// 移除之前的高亮
	const previousHighlights = contractContentRef.value.querySelectorAll('.risk-highlight');
	previousHighlights.forEach(el => {
		el.classList.remove('risk-highlight', 'risk-major', 'risk-general');
	});

	// 根据clause.id查找对应的HTML元素
	const targetElement = contractContentRef.value.querySelector(`#${clause.id}`);
	if (targetElement) {
		targetElement.classList.add('risk-highlight', riskLevel === 'major' ? 'risk-major' : 'risk-general');

		// 滚动到目标元素
		targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
	}
};

// 导出结果
const handleExportResults = () => {
	message.info("导出功能开发中...");
};

// 设置模拟合同内容
const setMockContractContent = () => {
	contractHtmlContent.value = `
		<div class="contract-document">
			<h1 class="text-2xl font-bold text-center mb-6">租赁合同</h1>

			<div class="contract-section mb-4" id="clause-1-1">
				<h2 class="text-lg font-semibold mb-2">甲方：北京某某房地产开发有限公司</h2>
				<p class="mb-2">法定代表人：张某某</p>
				<p class="mb-2">营业执照号：91110000000000000X</p>
				<p class="mb-2">注册地址：北京市朝阳区某某街道某某号</p>
				<p class="mb-2">联系电话：010-12345678</p>
			</div>

			<div class="contract-section mb-4" id="clause-1-2">
				<h2 class="text-lg font-semibold mb-2">乙方：</h2>
				<p class="mb-2">姓名/名称：</p>
				<p class="mb-2">身份证号/营业执照号：</p>
				<p class="mb-2">联系地址：</p>
				<p class="mb-2">联系电话：</p>
				<p class="text-gray-700">经双方协商同意签定下合同：</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第一条 租赁物业基本情况</h3>
				<p class="mb-2">甲方同意将其拥有的位于北京市朝阳区某某街道某某号的房屋（以下简称"租赁物业"）出租给乙方使用。</p>
				<p class="mb-2">租赁物业建筑面积约为120平方米，房屋用途为住宅，房屋现状良好。</p>
				<p class="mb-2">租赁物业包括但不限于：客厅、卧室、厨房、卫生间及相关配套设施。</p>
			</div>

			<div class="contract-section mb-4" id="clause-2-1">
				<h3 class="font-semibold mb-2">第二条 租赁期限</h3>
				<p class="mb-2">租赁期限自2024年1月1日起至2025年12月31日止，共计24个月。</p>
				<p class="mb-2">租赁期满，如乙方需要续租，应提前30日书面通知甲方，经甲方同意后可续签租赁合同。</p>
				<p class="mb-2">在同等条件下，乙方享有优先续租权。</p>
			</div>

			<div class="contract-section mb-4" id="clause-3-1">
				<h3 class="font-semibold mb-2">第三条 租金及支付方式</h3>
				<p class="mb-2">月租金为人民币8000元整（大写：捌仟元整）。</p>
				<p class="mb-2">租金支付方式：按季度支付，每季度租金为24000元。</p>
				<p class="mb-2">乙方应于每季度第一个月的5日前将当季度租金支付给甲方。</p>
			</div>

			<div class="contract-section mb-4" id="clause-3-2">
				<h3 class="font-semibold mb-2">第四条 押金</h3>
				<p class="mb-2">乙方应向甲方支付押金人民币16000元整（大写：壹万陆仟元整），相当于两个月租金。</p>
				<p class="mb-2">押金用于保证乙方履行合同义务，合同期满且乙方无违约行为时，甲方应无息退还押金。</p>
				<p class="mb-2">如乙方有违约行为或造成房屋损坏，甲方有权从押金中扣除相应费用。</p>
			</div>

			<div class="contract-section mb-4" id="clause-3-3">
				<h3 class="font-semibold mb-2">第五条 逾期付款责任</h3>
				<p class="mb-2">如乙方逾期支付租金，应按逾期金额每日0.05%向甲方支付违约金。</p>
				<p class="mb-2">逾期超过15日的，甲方有权解除合同并要求乙方承担违约责任。</p>
				<p class="mb-2">因乙方违约导致合同解除的，甲方有权没收押金。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第六条 房屋使用及维护</h3>
				<p class="mb-2">乙方应合理使用租赁物业，不得改变房屋结构和用途。</p>
				<p class="mb-2">乙方应承担租赁期间的水、电、燃气、物业管理费、网络费等日常费用。</p>
				<p class="mb-2">房屋设施设备的正常维修由甲方负责，因乙方使用不当造成的损坏由乙方承担维修费用。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第七条 装修约定</h3>
				<p class="mb-2">乙方如需对房屋进行装修改造，应事先征得甲方书面同意。</p>
				<p class="mb-2">装修应符合国家相关法律法规和小区管理规定。</p>
				<p class="mb-2">合同期满时，乙方不得拆除装修设施，甲方不予补偿。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第八条 转租限制</h3>
				<p class="mb-2">未经甲方书面同意，乙方不得将租赁物业全部或部分转租给第三方。</p>
				<p class="mb-2">乙方不得利用租赁物业从事违法活动。</p>
				<p class="mb-2">违反本条约定的，甲方有权解除合同并要求乙方承担违约责任。</p>
			</div>

			<div class="contract-section mb-4" id="clause-4-1">
				<h3 class="font-semibold mb-2">第九条 违约责任</h3>
				<p class="mb-2">任何一方违反本合同约定，应向守约方支付违约金人民币10000元。</p>
				<p class="mb-2">违约金不足以弥补损失的，违约方还应赔偿守约方的实际损失。</p>
				<p class="mb-2">因不可抗力导致合同无法履行的，双方均不承担违约责任。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第十条 合同解除</h3>
				<p class="mb-2">有下列情形之一的，甲方有权解除合同：</p>
				<p class="mb-2">1. 乙方逾期支付租金超过15日的；</p>
				<p class="mb-2">2. 乙方擅自转租房屋的；</p>
				<p class="mb-2">3. 乙方利用房屋从事违法活动的；</p>
				<p class="mb-2">4. 乙方严重损坏房屋的。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第十一条 争议解决</h3>
				<p class="mb-2">因本合同发生的争议，双方应协商解决。</p>
				<p class="mb-2">协商不成的，可向房屋所在地人民法院起诉。</p>
			</div>

			<div class="contract-section mb-4">
				<h3 class="font-semibold mb-2">第十二条 其他约定</h3>
				<p class="mb-2">本合同未尽事宜，双方可另行协商并签订补充协议。</p>
				<p class="mb-2">补充协议与本合同具有同等法律效力。</p>
				<p class="mb-2">本合同一式两份，甲乙双方各执一份，自双方签字盖章之日起生效。</p>
			</div>

			<div class="contract-signature mt-8">
				<div class="flex justify-between">
					<div class="text-center">
						<p class="mb-4 text-red-500 font-semibold">甲方（出租方）：</p>
						<p class="mb-2">北京某某房地产开发有限公司</p>
						<p class="mb-2">法定代表人：张某某</p>
						<p class="mb-2">联系电话：010-12345678</p>
						<p class="mb-4">签字：_______________</p>
					</div>
					<div class="text-center">
						<p class="mb-4 text-red-500 font-semibold">乙方（承租方）：</p>
						<p class="mb-2">姓名：_______________</p>
						<p class="mb-2">身份证号：_______________</p>
						<p class="mb-2">联系电话：_______________</p>
						<p class="mb-4">签字：_______________</p>
					</div>
				</div>
				<div class="text-center mt-8">
					<p class="text-lg font-semibold">签约日期：二〇二四年 月 日</p>
				</div>
			</div>
		</div>
	`;
};

// 组件挂载时检查路由参数
onMounted(() => {
	// 如果从合同清单页面跳转过来，可以获取传递的参数
	if (route.query.scenario) {
		contractScenario.value = route.query.scenario as string;
	}
	if (route.query.stance) {
		reviewStance.value = route.query.stance as string;
	}
});
</script>

<style scoped>
/* 自定义样式 */
.bg-clip-text {
	-webkit-background-clip: text;
	background-clip: text;
}

/* 主标题动画效果 */
h1 {
	background-size: 200% 200%;
	animation: gradient 3s ease infinite;
}

@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}

/* 按钮悬停效果增强 */
.gradient-button {
	background-size: 200% 200%;
	animation: gradient-button 3s ease infinite;
	box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.3);
}

.gradient-button:hover {
	box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
	transform: translateY(-2px);
}

@keyframes gradient-button {
	0% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}

/* 优雅的文件上传区域样式 */
.upload-area-elegant {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	background: linear-gradient(135deg,
			rgba(255, 255, 255, 0.9) 0%,
			rgba(248, 250, 252, 0.95) 100%);
	backdrop-filter: blur(10px);
	border-color: rgba(59, 130, 246, 0.2);
}

.upload-area-elegant::before {
	content: '';
	position: absolute;
	inset: 0;
	padding: 2px;
	background: linear-gradient(45deg,
			rgba(59, 130, 246, 0.1),
			rgba(147, 197, 253, 0.1),
			rgba(219, 234, 254, 0.1),
			rgba(59, 130, 246, 0.1));
	background-size: 300% 300%;
	border-radius: inherit;
	mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
	mask-composite: xor;
	animation: subtleBorderFlow 8s ease infinite;
}

.upload-bg-elegant {
	background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
		radial-gradient(circle at 80% 70%, rgba(147, 197, 253, 0.05) 0%, transparent 50%),
		linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
}

.upload-pattern {
	background-image:
		radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
		radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.03) 1px, transparent 1px);
	background-size: 40px 40px, 60px 60px;
	animation: patternFloat 20s linear infinite;
}

.upload-area-elegant:hover {
	transform: translateY(-2px);
	border-color: rgba(59, 130, 246, 0.4);
	box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1),
		0 0 40px rgba(147, 197, 253, 0.1);
	background: linear-gradient(135deg,
			rgba(255, 255, 255, 0.95) 0%,
			rgba(248, 250, 252, 1) 100%);
}

.upload-area-elegant:hover .upload-pattern {
	animation-duration: 10s;
}

/* 优雅的装饰性元素 */
.decoration-dot {
	position: absolute;
	border-radius: 50%;
	background: rgba(59, 130, 246, 0.1);
	animation: gentleFloat 8s ease-in-out infinite;
}

.decoration-dot-1 {
	width: 8px;
	height: 8px;
	top: 25%;
	left: 15%;
	animation-delay: 0s;
}

.decoration-dot-2 {
	width: 6px;
	height: 6px;
	top: 65%;
	right: 20%;
	animation-delay: 3s;
}

.decoration-dot-3 {
	width: 10px;
	height: 10px;
	bottom: 25%;
	left: 25%;
	animation-delay: 6s;
}

.decoration-line {
	position: absolute;
	background: linear-gradient(90deg,
			transparent 0%,
			rgba(59, 130, 246, 0.1) 50%,
			transparent 100%);
	animation: lineGlow 6s ease-in-out infinite;
}

.decoration-line-1 {
	width: 60px;
	height: 1px;
	top: 35%;
	right: 15%;
	transform: rotate(45deg);
	animation-delay: 1s;
}

.decoration-line-2 {
	width: 40px;
	height: 1px;
	bottom: 35%;
	right: 25%;
	transform: rotate(-30deg);
	animation-delay: 4s;
}

/* 优雅的上传图标样式 */
.upload-icon-container {
	position: relative;
}

.upload-icon {
	filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
	animation: gentleBounce 3s ease-in-out infinite;
	transition: all 0.3s ease;
}

.upload-area-elegant:hover .upload-icon {
	transform: scale(1.1);
	filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

.upload-text {
	transition: all 0.3s ease;
}

.upload-area-elegant:hover .upload-text {
	color: #374151;
}

.upload-description {
	transition: all 0.3s ease;
}

/* 优雅的动画关键帧 */
@keyframes subtleBorderFlow {
	0% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}

	100% {
		background-position: 0% 50%;
	}
}

@keyframes patternFloat {
	0% {
		background-position: 0px 0px, 0px 0px;
	}

	100% {
		background-position: 40px 40px, 60px 60px;
	}
}

@keyframes gentleFloat {

	0%,
	100% {
		transform: translateY(0px);
		opacity: 0.3;
	}

	50% {
		transform: translateY(-3px);
		opacity: 0.6;
	}
}

@keyframes lineGlow {

	0%,
	100% {
		opacity: 0.1;
		transform: scaleX(0.8);
	}

	50% {
		opacity: 0.3;
		transform: scaleX(1);
	}
}

@keyframes gentleBounce {

	0%,
	100% {
		transform: translateY(0);
	}

	50% {
		transform: translateY(-2px);
	}
}

/* 任务列表项悬停效果 */
.task-item {
	transition: all 0.2s ease;
}

.task-item:hover {
	transform: translateX(4px);
	box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 反向旋转动画 */
@keyframes spin-reverse {
	from {
		transform: rotate(360deg);
	}

	to {
		transform: rotate(0deg);
	}
}

.animate-spin-reverse {
	animation: spin-reverse 1s linear infinite;
}

/* 自定义loader样式 - 蓝紫渐变动画 */
.loader {
	--R: 40px;
	--g1: #3b82f6 96%, #0000;
	/* 旋转部分：蓝色 - 对应blue-500 */
	--g2: #e2e8f0 96%, #0000;
	/* 底色：低饱和度浅灰 */
	width: calc(2*var(--R));
	aspect-ratio: 1;
	border-radius: 50%;
	display: grid;
	-webkit-mask: linear-gradient(#000 0 0);
	mask: linear-gradient(#000 0 0);
	animation: l30 2s infinite linear, colorShift 4s ease-in-out infinite;
}

.loader::before,
.loader::after {
	content: "";
	grid-area: 1/1;
	width: 50%;
	background:
		radial-gradient(farthest-side, var(--g1)) calc(var(--R) + 0.866*var(--R) - var(--R)) calc(var(--R) - 0.5*var(--R) - var(--R)),
		radial-gradient(farthest-side, var(--g1)) calc(var(--R) + 0.866*var(--R) - var(--R)) calc(var(--R) - 0.5*var(--R) - var(--R)),
		radial-gradient(farthest-side, var(--g2)) calc(var(--R) + 0.5*var(--R) - var(--R)) calc(var(--R) - 0.866*var(--R) - var(--R)),
		radial-gradient(farthest-side, var(--g1)) 0 calc(-1*var(--R)),
		radial-gradient(farthest-side, var(--g2)) calc(var(--R) - 0.5*var(--R) - var(--R)) calc(var(--R) - 0.866*var(--R) - var(--R)),
		radial-gradient(farthest-side, var(--g1)) calc(var(--R) - 0.866*var(--R) - var(--R)) calc(var(--R) - 0.5*var(--R) - var(--R)),
		radial-gradient(farthest-side, var(--g2)) calc(-1*var(--R)) 0,
		radial-gradient(farthest-side, var(--g1)) calc(var(--R) - 0.866*var(--R) - var(--R)) calc(var(--R) + 0.5*var(--R) - var(--R));
	background-size: calc(2*var(--R)) calc(2*var(--R));
	background-repeat: no-repeat;
}

.loader::after {
	transform: rotate(180deg);
	transform-origin: right;
}

@keyframes l30 {
	100% {
		transform: rotate(-1turn)
	}
}

/* 旋转部分蓝紫渐变动画 */
@keyframes colorShift {
	0% {
		filter: hue-rotate(0deg) saturate(1.0) brightness(1.0);
	}

	50% {
		filter: hue-rotate(45deg) saturate(1.1) brightness(1.05);
	}

	100% {
		filter: hue-rotate(0deg) saturate(1.0) brightness(1.0);
	}
}

/* 中等尺寸loader用于中间部分 */
.loader-medium {
	--R: 35px;
}

/* 大尺寸loader用于右边部分 */
.loader-large {
	--R: 30px;
}

/* 合同内容样式 */
.contract-content {
	line-height: 1.8;
	font-size: 14px;
	color: #333;
}

.contract-content h1 {
	font-size: 24px;
	font-weight: bold;
	text-align: center;
	margin-bottom: 24px;
	color: #1a1a1a;
}

.contract-content h2 {
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 12px;
	color: #2d3748;
}

.contract-content h3 {
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 8px;
	color: #2d3748;
}

.contract-content p {
	margin-bottom: 8px;
	color: #4a5568;
}

.contract-section {
	margin-bottom: 16px;
	padding: 12px;
	border-radius: 4px;
	transition: all 0.3s ease;
}

.contract-signature {
	margin-top: 32px;
	padding: 16px;
	border-top: 1px solid #e2e8f0;
}

/* 风险高亮样式 */
.risk-highlight {
	position: relative;
	transition: all 0.3s ease;
	border-radius: 4px;
	padding: 8px;
	margin: 4px 0;
}

.risk-major {
	background-color: rgba(239, 68, 68, 0.1);
	border: 2px solid #ef4444;
	box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

.risk-general {
	background-color: rgba(245, 158, 11, 0.1);
	border: 2px solid #f59e0b;
	box-shadow: 0 0 10px rgba(245, 158, 11, 0.2);
}

.risk-highlight::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border-radius: 6px;
	z-index: -1;
	animation: riskPulse 2s ease-in-out infinite;
}

.risk-major::before {
	background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);
}

.risk-general::before {
	background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.1), transparent);
}

@keyframes riskPulse {

	0%,
	100% {
		opacity: 0.5;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.02);
	}
}

/* 暗色模式下的合同内容样式 */
.dark .contract-content {
	color: #e2e8f0;
}

.dark .contract-content h1,
.dark .contract-content h2,
.dark .contract-content h3 {
	color: #f7fafc;
}

.dark .contract-content p {
	color: #cbd5e0;
}

.dark .contract-signature {
	border-top-color: #4a5568;
}

.dark .risk-major {
	background-color: rgba(239, 68, 68, 0.2);
	border-color: #ef4444;
}

.dark .risk-general {
	background-color: rgba(245, 158, 11, 0.2);
	border-color: #f59e0b;
}
</style>
