<script setup lang="ts">
import McInput from './mcInput.vue';
import mcList from './mcList.vue';
import mcplayer from './mcplayer.vue';

</script>

<template>

<div class="flex w-full h-full   music-content">
    <div class="w-[300px] h-full  overflow-y-auto ">
        <McInput />
    </div>
    <div class=" flex-1  h-full bg-[#fafbfc] pt-2 dark:bg-[#18181c] overflow-y-auto music-list" >
        <mcList/>
    </div>
    <div class="w-[300px]  h-full overflow-y-auto ">
        <mcplayer/>
    </div>
</div>
</template>