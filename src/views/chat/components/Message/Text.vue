<script lang="ts" setup>
import { computed, onMounted, onUnmounted, onUpdated, ref ,nextTick} from 'vue'
import MarkdownIt from 'markdown-it'
import mdKatex from '@traptitech/markdown-it-katex'
import mila from 'markdown-it-link-attributes'
import hljs from 'highlight.js'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { copyToClip } from '@/utils/copy'

import mjText from '@/views/mj/mjText.vue'
import dallText from '@/views/mj/dallText.vue'
import ttsText from '@/views/mj/ttsText.vue'
import whisperText from '@/views/mj/whisperText.vue'
import MjTextAttr from '@/views/mj/mjTextAttr.vue'
import aiTextSetting from '@/views/mj/aiTextSetting.vue'
import aiSetAuth from '@/views/mj/aiSetAuth.vue'
import { isApikeyError, isAuthSessionError, isTTS, mlog } from '@/api'

import * as echarts from 'echarts'

interface Props {
  inversion?: boolean
  error?: boolean
  text?: string
  loading?: boolean
  asRawText?: boolean
  chat: Chat.Chat
}

const props = defineProps<Props>()

const { isMobile } = useBasicLayout()
const textRef = ref<HTMLElement>()

const mdi = new MarkdownIt({
  html: false,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })

const wrapClass = computed(() => {
  return [
    'text-wrap',
    'min-w-[20px]',
    'max-w-[810px]',
    'rounded-md',
    isMobile.value ? 'p-2' : 'px-3 pb-2',
    props.inversion ? 'bg-[#d2f9d1]' : 'bg-[#f4f6f8]',
    props.inversion ? 'dark:bg-[#a1dc95]' : 'dark:bg-[#1e1e20]',
    props.inversion ? 'message-request' : 'message-reply',
    { 'text-red-500': props.error },
  ]
})

const text = computed(() => {
  let value = props.text ?? ''
  if (!props.asRawText) {
    value = value.replace(/\\\( *(.*?) *\\\)/g, '$$$1$$')
    value = value.replace(/\\\[ *(.*?) *\\\]/g, '$$$$$1$$$$')
    value = value.replaceAll('\\[', '$$$$')
    value = value.replaceAll('\\]', '$$$$')

    value = value.replace(/<think>([\s\S]*?)(?=<\/think>|$)/g, (match: string, content: string) => {
      const processedContent: string = content
          .split('\n')
          .map(line => line.trim() ? '>' + line : line)
          .join('\n')
          .replace(/(\r?\n)+/g, '\n>\n')
      return '>Thinking...' + processedContent
    })
    value = value.replaceAll('</think>', '')
    return mdi.render(value)
  }
  return value
})

function highlightBlock(str: string, lang?: string) {
  if (lang === 'echarts') {
    // ✅ PATCH: 兼容 ````echarts` 语法
    return `<div class="echarts-container"  style="position: relative; z-index: 10;" data-chart='${encodeURIComponent(str)}'></div>`
  }

  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t('chat.copyCode')}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

function addCopyEvents() {
  if (!textRef.value) return
  const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
  copyBtn.forEach((btn) => {
    btn.addEventListener('click', () => {
      const code = btn.parentElement?.nextElementSibling?.textContent
      if (code) {
        copyToClip(code).then(() => {
          btn.textContent = '复制成功'
          setTimeout(() => {
            btn.textContent = '复制代码'
          }, 1000)
        })
      }
    })
  })
}

// ✅ 新增：防抖 + MutationObserver
let observer: MutationObserver
let renderTimer: ReturnType<typeof setTimeout>
const chartMap = new WeakMap<HTMLElement, echarts.ECharts>()   // 容器 -> 实例
const allCharts = new Set<echarts.ECharts>()                   // 统一销毁

onUnmounted(() => {
  allCharts.forEach(c => c.dispose())
  allCharts.clear()
})

function renderECharts() {
  clearTimeout(renderTimer)
  renderTimer = setTimeout(() => {
    nextTick(() => {
      if (!textRef.value) return

      /* 1️⃣ 标准 ```echarts 语法（节点已存在，只 init 一次） */
      textRef.value.querySelectorAll('.echarts-container[data-chart]').forEach(container => {
        if (chartMap.has(container)) {
          chartMap.get(container)!.setOption(
              JSON.parse(decodeURIComponent(container.getAttribute('data-chart')!))
          )
          return
        }
        const chart = echarts.init(container)
        chart.setOption(JSON.parse(decodeURIComponent(container.getAttribute('data-chart')!)))
        chartMap.set(container, chart)
        allCharts.add(chart)
      })

      /* 2️⃣ 兼容 <pre><code> JSON：就地渲染，永不替换节点 */
      textRef.value.querySelectorAll('.code-block-body').forEach(block => {
        const text = block.textContent?.trim()
        if (!text) return
        try {
          const option = JSON.parse(text)

          /* 2-1 找到或原地创建容器（不再额外插入） */
          let container = block.parentElement as HTMLElement
          if (!container.classList.contains('echarts-container')) {
            container.className = 'echarts-container'
            container.style.height = '400px'
            container.innerHTML = ''   // 清空原 <pre><code>
          }

          /* 2-2 只 init 一次 */
          if (!chartMap.has(container)) {
            const chart = echarts.init(container)
            chartMap.set(container, chart)
            allCharts.add(chart)
          }
          const chart = chartMap.get(container)!

          /* 2-3 统一补全（你的旧逻辑） */
          if (option.series?.[0]?.type === 'pie') {
            option.series[0].radius = ['40%', '70%']
          }
          if (!option.tooltip) {
            option.tooltip = {
              trigger: option.series?.[0]?.type === 'pie' ? 'item' : 'axis',
              formatter:
                  option.series?.[0]?.type === 'pie'
                      ? '{a}<br/>{b}: {c} ({d}%)'
                      : '{a}<br/>{b}: {c}'
            }
          }
          option.legend = {
            selectedMode: true,
            ...(option.series?.[0]?.type === 'pie'
                ? { top: '5%', orient: 'vertical', left: 'left' }
                : { top: '5%', left: 'center' })
          }
          if (option.title) {
            option.title.left = option.series?.[0]?.type === 'pie' ? 'center' : 'left'
          }

          chart.setOption(option, true)
        } catch {}
      })
    })
  }, 50)
}

onMounted(() => {
  addCopyEvents()
  renderECharts()

  // ✅ 监听 DOM 变化（实时重新渲染）
  observer = new MutationObserver(() => renderECharts())
  if (textRef.value) {
    observer.observe(textRef.value, { childList: true, subtree: true })
  }
})

onUpdated(() => {
  addCopyEvents()
  renderECharts() // ✅ 已防抖
})


</script>

<template>
  <div class="text-black" :class="wrapClass">
    <div ref="textRef" class="leading-relaxed break-words">
      <div v-if="!inversion">
        <aiTextSetting v-if="isApikeyError(text)" />
        <aiSetAuth v-if="isAuthSessionError(text)" />

        <dallText v-if="chat.model && !chat.model.includes('chat')" :chat="chat" class="whitespace-pre-wrap" />
        <mjText v-if="chat.mjID" :chat="chat" :mdi="mdi" class="whitespace-pre-wrap" />
        <ttsText v-else-if="chat.model && isTTS(chat.model) && chat.text === 'ok'" :chat="chat" />
        <template v-else>
          <div v-if="!asRawText" class="markdown-body" :class="{ 'markdown-body-generate': loading }" v-html="text" />
          <div v-else class="whitespace-pre-wrap" v-text="text" />
        </template>
      </div>

      <whisperText v-else-if="text === 'whisper' && chat.opt?.lkey" :chat="chat" />
      <div v-else-if="asRawText" class="whitespace-pre-wrap" v-text="text" />
      <div v-else class="markdown-body" style="--color-fg-default:#24292f" v-html="text" />

      <MjTextAttr v-if="chat.opt?.images?.length" :image="chat.opt.images[0]" />
      <whisperText v-if="chat.model?.includes('whisper') && chat.opt?.lkey" :isW="true" :chat="chat" class="w-full" />
      <ttsText v-if="!inversion && chat.opt?.duration && chat.opt.duration > 0 && chat.opt?.lkey" :isW="true" :chat="chat" class="w-full" />
    </div>
  </div>
</template>

<style lang="less">
@import url(./style.less);

// ✅ PATCH: 可选样式增强
.echarts-container {
  pointer-events: auto !important;
  z-index: 1;
  position: relative;
  width: 100%;
  height: 400px;
  margin: 16px 0;
  border-radius: 8px;
  background: #fff;
}
</style>